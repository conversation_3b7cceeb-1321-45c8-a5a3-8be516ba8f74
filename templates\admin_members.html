{% extends "layout.html" %}
{% block content %}
<div class="admin-members">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <div>
      <h2 class="mb-1">Members</h2>
      <p class="text-muted mb-0">View subscription status and manage account state</p>
    </div>
    <div class="admin-nav d-flex gap-2">
      <a href="/admin/applications" class="btn btn-outline-secondary">Applications</a>
      <a href="/admin/board-requests" class="btn btn-outline-secondary">Board Requests</a>
      <a href="/admin/members" class="btn btn-primary">Members</a>
      <a href="/admin/settings/boards" class="btn btn-outline-secondary">Boards</a>
    </div>
  </div>

  <div class="table-responsive">
    <table class="table table-striped align-middle">
      <thead>
        <tr>
          <th scope="col">User</th>
          <th scope="col">Email</th>
          <th scope="col">Tier</th>
          <th scope="col">Status</th>
          <th scope="col">Subscription</th>
          <th scope="col">Actions</th>
        </tr>
      </thead>
      <tbody>
        {% for u in users %}
        {% set is_expired = (u.subscription_expires_at is not none and u.subscription_expires_at < now) %}
        <tr>
          <td class="col-user">
            <div class="d-flex flex-column">
              <strong class="text-white">{{ u.username }}</strong>
              {% if u.name %}<small class="text-muted">{{ u.name }}</small>{% endif %}
            </div>
          </td>
          <td class="col-email">
            <a href="mailto:{{ u.email }}" class="email-link">{{ u.email }}</a>
          </td>
          <td class="col-tier"><span class="tier-badge tier-{{ (u.tier or 'initiate')|lower }}">{{ u.tier or 'Initiate' }}</span></td>
          <td class="col-status">
            {% if u.is_suspended %}
              <span class="badge bg-danger">Suspended</span>
            {% elif is_expired %}
              <span class="badge bg-warning text-dark">Expired</span>
            {% else %}
              <span class="badge bg-success">Active</span>
            {% endif %}
          </td>
          <td class="col-subscription">
            {% if u.subscription_expires_at %}
              <span title="{{ u.subscription_expires_at }}">
                Expires {{ u.subscription_expires_at.strftime('%Y-%m-%d') }}
              </span>
              {% if is_expired %}<small class="text-danger ms-1">(past due)</small>{% endif %}
            {% else %}
              <span class="text-muted">N/A</span>
            {% endif %}
          </td>
          <td class="col-actions">
            <div class="d-flex gap-2 flex-wrap">
              {% if not u.is_suspended %}
                <a href="/admin/member/{{ u.id }}/suspend" class="btn btn-sm btn-outline-danger" onclick="return confirm('Suspend {{ u.username }}?')">
                  <i class="bi bi-slash-circle me-1"></i>Suspend
                </a>
              {% else %}
                <a href="/admin/member/{{ u.id }}/reactivate" class="btn btn-sm btn-outline-success" onclick="return confirm('Reactivate {{ u.username }}?')">
                  <i class="bi bi-check-circle me-1"></i>Reactivate
                </a>
              {% endif %}
            </div>
          </td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
</div>

<style>
.admin-members .table thead th {
  background-color: var(--color-bg-secondary);
  color: var(--color-text-white);
  border-bottom: 2px solid var(--color-primary);
}
.admin-members .table tbody td { border-bottom: 1px solid var(--color-border); }
.admin-members .col-user { width: 22%; min-width: 180px; }
.admin-members .col-email { width: 24%; min-width: 220px; word-break: break-all; }
.admin-members .col-tier { width: 10%; white-space: nowrap; }
.admin-members .col-status { width: 12%; white-space: nowrap; }
.admin-members .col-subscription { width: 18%; white-space: nowrap; }
.admin-members .col-actions { width: 14%; }

.email-link { color: var(--color-primary); text-decoration: none; }
.email-link:hover { text-decoration: underline; }

.tier-badge { padding: 2px 6px; border-radius: 999px; font-size: 11px; font-weight: 600; text-transform: uppercase; }
.tier-initiate { background: rgba(255,255,255,0.08); color: var(--color-text-white); }
.tier-contributor { background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-light) 100%); color: var(--color-bg-primary); }
.tier-sentinel { background: linear-gradient(135deg, #cd7f32 0%, #b8860b 100%); color: white; }
.tier-patron { background: linear-gradient(135deg, #e5e4e2 0%, #c0c0c0 100%); color: var(--color-bg-primary); }
</style>
{% endblock %}

