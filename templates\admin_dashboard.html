{% extends "layout.html" %}
{% block content %}
<div class="admin-dashboard">
  <div class="dashboard-header mb-4">
    <h2>Admin Dashboard</h2>
    <p class="lead">Manage membership applications and community oversight</p>
  </div>
  <div class="admin-nav d-flex gap-2 mb-3">
    <a href="/admin/applications" class="btn btn-primary">Applications</a>
    <a href="/admin/board-requests" class="btn btn-outline-secondary">Board Requests</a>
    <a href="/admin/members" class="btn btn-outline-secondary">Members</a>
    <a href="/admin/settings/boards" class="btn btn-outline-secondary">Boards</a>
  </div>


  <!-- Dashboard Stats -->
  <div class="row mb-4">
    <div class="col-md-4">
      <div class="stat-card card text-center">
        <div class="card-body">
          <h3 class="stat-number">{{ applications|length }}</h3>
          <p class="stat-label">Pending Applications</p>
        </div>
      </div>
    </div>
    <div class="col-md-4">
      <div class="stat-card card text-center">
        <div class="card-body">
          <h3 class="stat-number">-</h3>
          <p class="stat-label">Total Members</p>
        </div>
      </div>
    </div>
    <div class="col-md-4">
      <div class="stat-card card text-center">
        <div class="card-body">
          <h3 class="stat-number">-</h3>
          <p class="stat-label">Active Today</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Applications Section -->
  <div class="applications-section">
    <div class="d-flex justify-content-between align-items-center mb-3">
      <h3>Pending Applications</h3>
      {% if applications %}
        <small class="text-muted">{{ applications|length }} application{{ 's' if applications|length != 1 else '' }} awaiting review</small>
      {% endif %}
    </div>

    {% if applications %}
      <div class="table-responsive">
        <table class="table table-striped">
          <thead>
            <tr>
              <th scope="col">Applicant</th>
              <th scope="col">Contact</th>
              <th scope="col">Desired Tier</th>
              <th scope="col">Application Reason</th>
              <th scope="col">Actions</th>
            </tr>
          </thead>
          <tbody>
            {% for app in applications %}
            <tr class="application-row">
              <td>
                <div class="applicant-info">
                  <strong>{{ app.username }}</strong>
                  {% if app.name %}
                    <br><small class="text-muted">{{ app.name }}</small>
                  {% endif %}
                </div>
              </td>
              <td>
                <a href="mailto:{{ app.email }}" class="email-link">{{ app.email }}</a>
              </td>
              <td>
                <span class="tier-badge tier-{{ app.tier.lower() }}">{{ app.tier }}</span>
              </td>
              <td>
                <div class="reason-text-container">
                  <div class="reason-preview">
                    {{ app.reason[:150] }}{% if app.reason|length > 150 %}...{% endif %}
                  </div>
                  {% if app.reason|length > 150 %}
                    <button class="btn btn-link btn-sm p-0 mt-1" onclick="toggleFullReason({{ loop.index }})">
                      <small><i class="bi bi-chevron-down me-1"></i>Read more</small>
                    </button>
                    <div id="full-reason-{{ loop.index }}" class="full-reason mt-2" style="display: none;">
                      <div class="reason-full-text">
                        {{ app.reason }}
                      </div>
                    </div>
                  {% endif %}
                </div>
              </td>
              <td>
                <div class="action-buttons">
                  <a class="btn btn-success btn-sm mb-1"
                     href="{{ url_for('admin.approve_application', app_id=app.id) }}"
                     onclick="return confirm('Are you sure you want to approve this application?')">
                    <i class="bi bi-check-circle me-1"></i>Approve
                  </a>
                  <a class="btn btn-danger btn-sm"
                     href="{{ url_for('admin.deny_application', app_id=app.id) }}"
                     onclick="return confirm('Are you sure you want to deny this application?')">
                    <i class="bi bi-x-circle me-1"></i>Deny
                  </a>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    {% else %}
      <div class="empty-state text-center py-5">
        <div class="empty-icon mb-3">
          <i class="bi bi-clipboard-data" style="font-size: 3rem; opacity: 0.3; color: var(--color-text-tertiary);"></i>
        </div>
        <h4>No pending applications</h4>
        <p class="text-muted">All applications have been reviewed. New applications will appear here.</p>
      </div>
    {% endif %}
  </div>

  <!-- Quick Actions -->
  <div class="quick-actions mt-5">
    <h3>Quick Actions</h3>
    <div class="row">
      <div class="col-md-6">
        <div class="action-card card">
          <div class="card-body">
            <h5>Member Management</h5>
            <p>View and manage existing community members.</p>
            <a class="btn btn-primary" href="/admin/members">
              Manage Members
            </a>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="action-card card">
          <div class="card-body">
            <h5>Community Settings</h5>
            <p>Configure community rules and settings.</p>
            <a class="btn btn-primary" href="/admin/settings/boards">
              Settings
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// Toggle full reason display
function toggleFullReason(index) {
  const fullReason = document.getElementById(`full-reason-${index}`);
  const button = fullReason.previousElementSibling;

  if (fullReason.style.display === 'none' || !fullReason.style.display) {
    fullReason.style.display = 'block';
    button.innerHTML = '<small><i class="bi bi-chevron-up me-1"></i>Show less</small>';
  } else {
    fullReason.style.display = 'none';
    button.innerHTML = '<small><i class="bi bi-chevron-down me-1"></i>Read more</small>';
  }
}

// Auto-refresh every 30 seconds for new applications
setInterval(() => {
  // Only refresh if there are no pending actions
  if (!document.querySelector('.btn:hover')) {
    window.location.reload();
  }
}, 30000);
</script>

<style>
/* Additional styles for admin dashboard */
.stat-card {
  border-left: 4px solid var(--color-primary);
}

/* Application table improvements */
.application-row td {
  vertical-align: middle;
  padding: var(--spacing-md);
}

/* Column sizing for better readability */
.applications-section table thead th:nth-child(1),
.applications-section table tbody td:nth-child(1) { width: 20%; min-width: 180px; }
.applications-section table thead th:nth-child(2),
.applications-section table tbody td:nth-child(2) { width: 24%; min-width: 220px; word-break: break-all; }
.applications-section table thead th:nth-child(3),
.applications-section table tbody td:nth-child(3) { width: 12%; white-space: nowrap; }
.applications-section table thead th:nth-child(4),
.applications-section table tbody td:nth-child(4) { width: 32%; }
.applications-section table thead th:nth-child(5),
.applications-section table tbody td:nth-child(5) { width: 12%; min-width: 120px; }

.reason-text-container {
  max-width: 460px;
  min-width: 260px;
}

.reason-preview {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  line-height: 1.4;
  color: var(--color-text-secondary);
}

.reason-full-text {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  line-height: 1.5;
  color: var(--color-text-primary);
  background: var(--color-bg-secondary);
  padding: var(--spacing-sm);
  border-radius: var(--radius-sm);
  border-left: 3px solid var(--color-primary);
  white-space: pre-wrap;
}

.tier-badge {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  text-transform: uppercase;
}

.tier-sentinel {
  background: rgba(205, 127, 50, 0.2);
  color: #cd7f32;
  border: 1px solid rgba(205, 127, 50, 0.3);
}

.tier-contributor {
  background: var(--color-primary-alpha);
  color: var(--color-primary);
  border: 1px solid var(--color-primary);
}

.tier-patron {
  background: rgba(229, 228, 226, 0.2);
  color: #e5e4e2;
  border: 1px solid rgba(229, 228, 226, 0.3);
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  min-width: 100px;
}

.email-link {
  color: var(--color-primary);
  text-decoration: none;
  word-break: break-all;
}

.email-link:hover {
  color: var(--color-primary-light);
  text-decoration: underline;
}

.applicant-info strong {
  color: var(--color-text-white);
  display: block;
  margin-bottom: var(--spacing-xs);
}

/* Responsive table improvements */
@media (max-width: 1200px) {
  .reason-text-container {
    max-width: 250px;
  }
}

@media (max-width: 992px) {
  .table-responsive {
    font-size: var(--font-size-sm);
  }

  .reason-text-container {
    max-width: 200px;
  }

  .action-buttons {
    flex-direction: row;
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .application-row td {
    padding: var(--spacing-sm);
  }

  .reason-text-container {
    max-width: 150px;
  }

  .tier-badge {
    font-size: 10px;
    padding: 2px 4px;
  }
}

.stat-number {
  font-size: 2rem;
  color: var(--color-primary);
  margin-bottom: 0;
}

.stat-label {
  color: var(--color-text-secondary);
  margin-bottom: 0;
  font-size: var(--font-size-sm);
}

.application-row:hover {
  background-color: rgba(212, 175, 55, 0.05);
}

.applicant-info strong {
  color: var(--color-text-white);
}

.email-link {
  color: var(--color-primary);
  text-decoration: none;
}

.email-link:hover {
  text-decoration: underline;
}

/* Admin table theming (ensure consistent dark theme) */
.admin-dashboard .table { background-color: transparent; }
.admin-dashboard .table thead th {
  background-color: var(--color-bg-secondary);
  color: var(--color-text-white);
  border-bottom: 2px solid var(--color-primary);
}
.admin-dashboard .table tbody tr { background-color: var(--color-bg-tertiary); }
.admin-dashboard .table tbody td { border-bottom: 1px solid var(--color-border); }
.admin-dashboard .table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(255, 255, 255, 0.03);
}
.admin-dashboard .table-striped tbody tr:hover {
  background-color: rgba(212, 175, 55, 0.08);
}


.tier-badge {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  text-transform: uppercase;
}

.tier-sentinel {
  background: linear-gradient(135deg, #cd7f32 0%, #b8860b 100%);
  color: white;
}

.tier-contributor {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
  color: var(--color-bg-primary);
}

.tier-patron {
  background: linear-gradient(135deg, #e5e4e2 0%, #c0c0c0 100%);
  color: var(--color-bg-primary);
}

.reason-text {
  max-width: 300px;
  line-height: var(--line-height-normal);
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  min-width: 120px;
}

.action-card {
  height: 100%;
}

.empty-state {
  background: rgba(255, 255, 255, 0.02);
  border-radius: var(--radius-lg);
  margin: var(--spacing-xl) 0;
}

@media (max-width: 768px) {
  .table-responsive {
    font-size: var(--font-size-sm);
  }

  .action-buttons {
    flex-direction: row;
    flex-wrap: wrap;
  }

  .reason-text {
    max-width: none;
  }
}
</style>
{% endblock %}
