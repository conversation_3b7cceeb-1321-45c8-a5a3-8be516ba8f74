{% extends "layout.html" %}
{% block content %}
<div class="admin-dashboard">
  <div class="dashboard-header mb-4">
    <h2>Admin Dashboard</h2>
    <p class="lead">Manage membership applications and community oversight</p>
  </div>
  <div class="admin-nav d-flex gap-2 mb-3">
    <a href="/admin/applications" class="btn btn-primary">Applications</a>
    <a href="/admin/board-requests" class="btn btn-outline-secondary">Board Requests</a>
    <a href="/admin/members" class="btn btn-outline-secondary">Members</a>
    <a href="/admin/settings/boards" class="btn btn-outline-secondary">Boards</a>
  </div>


  <!-- Dashboard Stats -->
  <div class="row mb-4">
    <div class="col-md-4">
      <div class="stat-card card text-center">
        <div class="card-body">
          <h3 class="stat-number">{{ applications|length }}</h3>
          <p class="stat-label">Pending Applications</p>
        </div>
      </div>
    </div>
    <div class="col-md-4">
      <div class="stat-card card text-center">
        <div class="card-body">
          <h3 class="stat-number">-</h3>
          <p class="stat-label">Total Members</p>
        </div>
      </div>
    </div>
    <div class="col-md-4">
      <div class="stat-card card text-center">
        <div class="card-body">
          <h3 class="stat-number">-</h3>
          <p class="stat-label">Active Today</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Applications Section -->
  <div class="applications-section">
    <div class="d-flex justify-content-between align-items-center mb-3">
      <h3>Pending Applications</h3>
      {% if applications %}
        <small class="text-muted">{{ applications|length }} application{{ 's' if applications|length != 1 else '' }} awaiting review</small>
      {% endif %}
    </div>

    {% if applications %}
      <div class="table-responsive">
        <table class="table table-striped">
          <thead>
            <tr>
              <th scope="col">Applicant</th>
              <th scope="col">Contact</th>
              <th scope="col">Desired Tier</th>
              <th scope="col">Application Reason</th>
              <th scope="col">Actions</th>
            </tr>
          </thead>
          <tbody>
            {% for app in applications %}
            <tr class="application-row">
              <td>
                <div class="applicant-info">
                  <strong>{{ app.username }}</strong>
                  {% if app.name %}
                    <br><small class="text-muted">{{ app.name }}</small>
                  {% endif %}
                </div>
              </td>
              <td>
                <a href="mailto:{{ app.email }}" class="email-link">{{ app.email }}</a>
              </td>
              <td>
                <span class="tier-badge tier-{{ app.tier.lower() }}">{{ app.tier }}</span>
              </td>
              <td>
                <div class="reason-text-container">
                  <div class="reason-preview">
                    {{ app.reason[:150] }}{% if app.reason|length > 150 %}...{% endif %}
                  </div>
                  {% if app.reason|length > 150 %}
                    <button class="btn btn-link btn-sm p-0 mt-1" onclick="toggleFullReason({{ loop.index }})">
                      <small><i class="bi bi-chevron-down me-1"></i>Read more</small>
                    </button>
                    <div id="full-reason-{{ loop.index }}" class="full-reason mt-2" style="display: none;">
                      <div class="reason-full-text">
                        {{ app.reason }}
                      </div>
                    </div>
                  {% endif %}
                </div>
              </td>
              <td>
                <div class="action-buttons">
                  <a class="btn btn-success btn-sm mb-1"
                     href="{{ url_for('admin.approve_application', app_id=app.id) }}"
                     onclick="return confirm('Are you sure you want to approve this application?')">
                    <i class="bi bi-check-circle me-1"></i>Approve
                  </a>
                  <a class="btn btn-danger btn-sm"
                     href="{{ url_for('admin.deny_application', app_id=app.id) }}"
                     onclick="return confirm('Are you sure you want to deny this application?')">
                    <i class="bi bi-x-circle me-1"></i>Deny
                  </a>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    {% else %}
      <div class="empty-state text-center py-5">
        <div class="empty-icon mb-3">
          <i class="bi bi-clipboard-data" style="font-size: 3rem; opacity: 0.3; color: var(--color-text-tertiary);"></i>
        </div>
        <h4>No pending applications</h4>
        <p class="text-muted">All applications have been reviewed. New applications will appear here.</p>
      </div>
    {% endif %}
  </div>

  <!-- Quick Actions -->
  <div class="quick-actions mt-5">
    <h3>Quick Actions</h3>
    <div class="row">
      <div class="col-md-6">
        <div class="action-card card">
          <div class="card-body">
            <h5>Member Management</h5>
            <p>View and manage existing community members.</p>
            <a class="btn btn-primary" href="/admin/members">
              Manage Members
            </a>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="action-card card">
          <div class="card-body">
            <h5>Community Settings</h5>
            <p>Configure community rules and settings.</p>
            <a class="btn btn-primary" href="/admin/settings/boards">
              Settings
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// Toggle full reason display
function toggleFullReason(index) {
  const fullReason = document.getElementById(`full-reason-${index}`);
  const button = fullReason.previousElementSibling;

  if (fullReason.style.display === 'none' || !fullReason.style.display) {
    fullReason.style.display = 'block';
    button.innerHTML = '<small><i class="bi bi-chevron-up me-1"></i>Show less</small>';
  } else {
    fullReason.style.display = 'none';
    button.innerHTML = '<small><i class="bi bi-chevron-down me-1"></i>Read more</small>';
  }
}

// Auto-refresh every 30 seconds for new applications
setInterval(() => {
  // Only refresh if there are no pending actions
  if (!document.querySelector('.btn:hover')) {
    window.location.reload();
  }
}, 30000);
</script>

{% endblock %}
