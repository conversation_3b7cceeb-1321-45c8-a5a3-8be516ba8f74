{% extends "layout.html" %}
{% block content %}
<div class="login-form">
  <div class="text-center mb-4">
    <h2>Welcome Back</h2>
    <p class="lead">Sign in to access your Vallum account</p>
  </div>

  <form method="POST" class="needs-validation" novalidate>
    <div class="form-group">
      <label for="email" class="form-label required">Email Address</label>
      <input type="email" class="form-control" id="email" name="email" placeholder="<EMAIL>" required autocomplete="email">
      <div class="invalid-feedback">
        Please provide a valid email address.
      </div>
    </div>

    <div class="form-group">
      <label for="password" class="form-label required">Password</label>
      <input type="password" class="form-control" id="password" name="password" placeholder="Enter your password" required autocomplete="current-password">
      <div class="invalid-feedback">
        Please enter your password.
      </div>
    </div>

    <div class="form-group">
      <div class="form-check">
        <input class="form-check-input" type="checkbox" id="remember" name="remember">
        <label class="form-check-label" for="remember">
          Remember me on this device
        </label>
      </div>
    </div>

    <div class="text-center">
      <button type="submit" class="btn btn-primary btn-lg w-100 mb-3">
        Sign In
      </button>

      <div class="login-links">
        <p class="mb-2">
          <small class="text-muted">
            Don't have an account? <a href="/apply">Apply to join</a>
          </small>
        </p>
        <p class="mb-0">
          <small class="text-muted">
            Forgot your password? <a href="#" onclick="alert('Please contact support for password reset.')">Contact support</a>
          </small>
        </p>
      </div>
    </div>
  </form>
</div>

<script>
// Enhanced form validation
(function() {
  'use strict';

  const form = document.querySelector('.needs-validation');

  form.addEventListener('submit', function(event) {
    if (!form.checkValidity()) {
      event.preventDefault();
      event.stopPropagation();
    }

    form.classList.add('was-validated');
  }, false);

  // Real-time validation feedback
  const inputs = form.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('blur', function() {
      if (this.checkValidity()) {
        this.classList.remove('is-invalid');
        this.classList.add('is-valid');
      } else {
        this.classList.remove('is-valid');
        this.classList.add('is-invalid');
      }
    });
  });
})();
</script>
{% endblock %}
