/* Vallum - Main Stylesheet */

/* ===== CSS CUSTOM PROPERTIES (DESIGN TOKENS) ===== */
:root {
  /* Colors */
  --color-primary: #d4af37;
  --color-primary-light: #f1c40f;
  --color-primary-dark: #b8941f;
  --color-primary-alpha: rgba(212, 175, 55, 0.25);
  --color-primary-alpha-light: rgba(212, 175, 55, 0.1);
  --color-primary-alpha-strong: rgba(212, 175, 55, 0.6);

  --color-bg-primary: #0e0e0e;
  --color-bg-secondary: #1a1a1a;
  --color-bg-tertiary: #111;
  --color-bg-quaternary: #1c1c1c;
  --color-bg-section-alt: #121212;

  --color-text-primary: #f5f5f5;
  --color-text-secondary: #e5e5e5;
  --color-text-tertiary: #c0c0c0;
  --color-text-white: #ffffff;

  --color-border: rgba(212, 175, 55, 0.2);
  --color-border-light: rgba(212, 175, 55, 0.3);

  /* Typography */
  --font-primary: 'Inter', sans-serif;
  --font-heading: 'Playfair Display', serif;
  --font-brand: 'Playfair Display', serif;

  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;

  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
  --line-height-loose: 2;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;
  --spacing-4xl: 5rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.25rem;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  --shadow-luxury: 0 0 25px rgba(0, 0, 0, 0.7);
  --shadow-luxury-hover: 0 0 40px var(--color-primary-alpha-strong);
  --shadow-glow: 0 0 12px var(--color-primary-alpha-strong);
  --shadow-glow-strong: 0 0 15px var(--color-primary-alpha-strong);

  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;

  /* Breakpoints (for reference in media queries) */
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --breakpoint-2xl: 1400px;
}

/* ===== BASE STYLES ===== */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--color-text-primary);
  background-color: var(--color-bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== ENHANCED TYPOGRAPHY SYSTEM ===== */

/* Base typography improvements */
body {
  text-rendering: optimizeLegibility;
  -webkit-font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
  font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
  font-variant-ligatures: common-ligatures;
}

/* Heading hierarchy with improved spacing and contrast */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-white);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-lg);
  margin-top: 0;
  letter-spacing: -0.025em;
  text-wrap: balance; /* Better text wrapping for headings */
}

h1 {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-xl);
  font-weight: var(--font-weight-bold);
  letter-spacing: -0.05em;
  line-height: 1.1;
}

h2 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  letter-spacing: -0.035em;
  line-height: 1.15;
}

h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  letter-spacing: -0.025em;
  line-height: 1.2;
}

h4 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  letter-spacing: -0.015em;
  line-height: 1.25;
}

h5 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  letter-spacing: -0.01em;
  line-height: 1.3;
}

h6 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  letter-spacing: 0;
  line-height: 1.35;
  text-transform: uppercase;
  color: var(--color-primary);
}

/* Enhanced paragraph styles */
p {
  line-height: var(--line-height-relaxed);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-md);
  margin-top: 0;
  text-wrap: pretty; /* Better text wrapping */
  hanging-punctuation: first last; /* Better punctuation handling */
}

p.lead {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-lg);
}

p.small, small {
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  color: var(--color-text-tertiary);
}

/* Link improvements */
a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--transition-normal);
  text-decoration-skip-ink: auto;
}

a:hover {
  color: var(--color-primary-light);
  text-decoration: underline;
  text-decoration-color: var(--color-primary-light);
  text-decoration-thickness: 2px;
  text-underline-offset: 3px;
}

a:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
  border-radius: 2px;
}

/* Text utilities */
.text-balance {
  text-wrap: balance;
}

.text-pretty {
  text-wrap: pretty;
}

.font-display {
  font-family: var(--font-heading);
}

.font-body {
  font-family: var(--font-primary);
}

.font-mono {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-variant-ligatures: none;
}

/* Font weight utilities */
.font-light {
  font-weight: var(--font-weight-light);
}

.font-normal {
  font-weight: var(--font-weight-normal);
}

.font-medium {
  font-weight: var(--font-weight-medium);
}

.font-semibold {
  font-weight: var(--font-weight-semibold);
}

.font-bold {
  font-weight: var(--font-weight-bold);
}

/* Letter spacing utilities */
.tracking-tight {
  letter-spacing: -0.025em;
}

.tracking-normal {
  letter-spacing: 0;
}

.tracking-wide {
  letter-spacing: 0.025em;
}

.tracking-wider {
  letter-spacing: 0.05em;
}

.tracking-widest {
  letter-spacing: 0.1em;
}

/* Line height utilities */
.leading-tight {
  line-height: var(--line-height-tight);
}

.leading-normal {
  line-height: var(--line-height-normal);
}

.leading-relaxed {
  line-height: var(--line-height-relaxed);
}

.leading-loose {
  line-height: var(--line-height-loose);
}

/* ===== RESPONSIVE TYPOGRAPHY ===== */
@media (max-width: 768px) {
  h1 {
    font-size: var(--font-size-3xl);
  }

  h2 {
    font-size: var(--font-size-2xl);
  }

  h3 {
    font-size: var(--font-size-xl);
  }
}

@media (max-width: 576px) {
  h1 {
    font-size: var(--font-size-2xl);
  }

  h2 {
    font-size: var(--font-size-xl);
  }

  h3 {
    font-size: var(--font-size-lg);
  }

  body {
    font-size: var(--font-size-sm);
  }
}

/* ===== NAVBAR STYLES ===== */
.navbar {
  background: linear-gradient(90deg, var(--color-bg-primary) 0%, var(--color-bg-secondary) 100%);
  padding: var(--spacing-md) var(--spacing-xl);
  border-bottom: 1px solid var(--color-border);
  position: sticky;
  top: 0;
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.navbar-brand {
  font-family: var(--font-brand);
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary) !important;
  letter-spacing: 2px;
  transition: transform var(--transition-normal);
}

.navbar-brand:hover {
  transform: scale(1.05);
  color: var(--color-primary-light) !important;
}

.navbar-text {
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
}

/* Mobile navbar improvements */
.navbar-toggler {
  border: 1px solid var(--color-primary);
  padding: var(--spacing-xs) var(--spacing-sm);
}

.navbar-toggler:focus {
  box-shadow: 0 0 0 0.2rem var(--color-primary-alpha);
}

.navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='%23d4af37' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

@media (max-width: 992px) {
  .navbar {
    padding: var(--spacing-md);
  }

  .navbar-brand {
    font-size: var(--font-size-xl);
  }
}

@media (max-width: 576px) {
  .navbar-brand {
    font-size: var(--font-size-lg);
    letter-spacing: 1px;
  }
}

/* ===== BUTTON STYLES ===== */
.btn-primary {
  border: 1px solid var(--color-primary);
  color: var(--color-primary);
  background-color: transparent;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-normal);
  cursor: pointer;
  display: inline-block;
  text-align: center;
  text-decoration: none;
  font-size: var(--font-size-sm);
}

.btn-primary:hover {
  background-color: var(--color-primary);
  color: var(--color-bg-primary);
  box-shadow: var(--shadow-glow);
  border-color: var(--color-primary);
  transform: translateY(-1px);
}

.btn-primary:focus {
  outline: none;
  box-shadow: 0 0 0 0.2rem var(--color-primary-alpha);
}

.btn-gold {
  border: 1px solid var(--color-primary);
  color: var(--color-primary);
  background-color: transparent;
  border-radius: var(--radius-full);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-normal);
  cursor: pointer;
  display: inline-block;
  text-align: center;
  text-decoration: none;
}

.btn-gold:hover {
  background-color: var(--color-primary);
  color: var(--color-bg-primary);
  box-shadow: var(--shadow-glow-strong);
  transform: translateY(-2px);
}

.btn-lg {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-lg);
}

.btn-sm {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-xs);
}

/* Button responsiveness */
@media (max-width: 576px) {
  .btn-lg {
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--font-size-base);
  }
}


/* ===== INTERACTIVE TRANSITIONS & OUTLINE BUTTONS ===== */
/* Ensure consistent smooth transitions across interactive elements */
.btn,
[class^="btn-outline-"],
[class*=" btn-outline-"],
.nav-link,
.form-control,
.form-select,
.card,
.navbar,
.modal,
.modal-dialog,
.modal-content {
  transition: all 0.3s ease-in-out;
}

/* Secondary button aligned with dark luxury theme */
.btn-secondary {
  background-color: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  color: var(--color-text-primary);
}
.btn-secondary:hover {
  background-color: var(--color-bg-primary);
  border-color: var(--color-primary);
  color: var(--color-text-white);
  box-shadow: var(--shadow-glow);
  transform: translateY(-1px);
}

/* Outline variants for consistent style */
.btn-outline-primary {
  border: 1px solid var(--color-primary);
  color: var(--color-primary);
  background: transparent;
}
.btn-outline-primary:hover {
  background: var(--color-primary);
  color: var(--color-bg-primary);
  box-shadow: var(--shadow-glow);
}

.btn-outline-secondary {
  border: 1px solid var(--color-border);
  color: var(--color-text-secondary);
  background: transparent;
}
.btn-outline-secondary:hover {
  background: var(--color-bg-secondary);
  color: var(--color-text-primary);
  border-color: var(--color-primary);
}

.btn-outline-warning {
  border: 1px solid var(--color-warning);
  color: var(--color-warning);
  background: transparent;
}
.btn-outline-warning:hover {
  background: var(--color-warning);
  color: var(--color-warning-dark);
}

.btn-outline-danger {
  border: 1px solid var(--color-error);
  color: var(--color-error);
  background: transparent;
}
.btn-outline-danger:hover {
  background: var(--color-error);
  color: #fff;
}

/* ===== CARD STYLES ===== */
.card {
  background: linear-gradient(145deg, var(--color-bg-tertiary), var(--color-bg-quaternary));
  border: 1px solid var(--color-primary-alpha);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
  box-shadow: var(--shadow-luxury);
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-luxury-hover);
}

.lux-card {
  background: linear-gradient(145deg, var(--color-bg-tertiary) 0%, var(--color-bg-quaternary) 100%);
  border: 1px solid var(--color-primary-alpha);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
  box-shadow: var(--shadow-luxury);
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.lux-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-luxury-hover);
}

.card-header {
  font-weight: var(--font-weight-medium);
  color: var(--color-text-white);
  border-bottom: 1px solid var(--color-border);
  padding-bottom: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.card-body {
  color: var(--color-text-secondary);
}

/* Card responsiveness */
@media (max-width: 768px) {
  .card, .lux-card {
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
  }
}

@media (max-width: 576px) {
  .card, .lux-card {
    padding: var(--spacing-md);
    border-radius: var(--radius-lg);
  }
}

/* ===== ALERT STYLES ===== */
.alert-info {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--color-border-light);
  color: var(--color-text-primary);
  border-radius: var(--radius-xl);
  backdrop-filter: blur(6px);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

/* ===== ENHANCED FORM STYLES ===== */

/* Form containers */
.form-group {
  margin-bottom: var(--spacing-lg);
  position: relative;
}

.form-floating {
  position: relative;
}

/* Enhanced form controls */
.form-control {
  background-color: rgba(255, 255, 255, 0.08);
  border: 2px solid var(--color-border);
  color: var(--color-text-primary);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  font-size: var(--font-size-base);
  font-family: var(--font-primary);
  transition: all var(--transition-normal);
  width: 100%;
  min-height: 48px; /* Accessibility: minimum touch target */
  font-weight: var(--font-weight-normal);
}

.form-control:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 0.2rem var(--color-primary-alpha),
              inset 0 1px 2px rgba(0, 0, 0, 0.1);
  background-color: rgba(255, 255, 255, 0.12);
  outline: none;
  transform: translateY(-1px);
  color: var(--color-text-white);
}

.form-control:hover:not(:focus) {
  border-color: var(--color-border-light);
  background-color: rgba(255, 255, 255, 0.07);
}

.form-control::placeholder {
  color: var(--color-text-tertiary);
  opacity: 0.8;
  transition: opacity var(--transition-normal);
}

.form-control:focus::placeholder {
  opacity: 0.5;
}

/* Improve typing experience in inputs */
.form-control {
  caret-color: var(--color-primary);
}

.form-control:not(:placeholder-shown) {
  background-color: rgba(255, 255, 255, 0.12);
  border-color: var(--color-border-light);
  color: var(--color-text-white);
}


/* Textarea specific styles */
textarea.form-control {
  min-height: 120px;
  resize: vertical;
  line-height: var(--line-height-relaxed);
}

/* Enhanced select styling */
.form-select {
  background-color: rgba(255, 255, 255, 0.08);
  border: 2px solid var(--color-border);
  color: var(--color-text-primary);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  font-size: var(--font-size-base);
  min-height: 48px;
  transition: all var(--transition-normal);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23d4af37' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right var(--spacing-md) center;
  background-size: 16px 12px;
  padding-right: calc(var(--spacing-md) * 3);
  cursor: pointer;
  font-weight: var(--font-weight-normal);
}

.form-select:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 0.2rem var(--color-primary-alpha);
  background-color: rgba(255, 255, 255, 0.12);
  outline: none;
  color: var(--color-text-white);
}

.form-select:hover:not(:focus) {
  border-color: var(--color-border-light);
  background-color: rgba(255, 255, 255, 0.10);
}

/* Select option styling */
.form-select option {
  background-color: var(--color-bg-secondary);
  color: var(--color-text-primary);
  padding: var(--spacing-sm);
}

.form-select option:checked {
  background-color: var(--color-primary);
  color: var(--color-bg-primary);
}

/* Keep select styling when actively choosing an option */
.form-select:active,
.form-select:focus-within {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 0.2rem var(--color-primary-alpha);
  background-color: rgba(255, 255, 255, 0.12);
  color: var(--color-text-white);
}

.form-select option:hover,
.form-select option:focus {
  background-color: rgba(212, 175, 55, 0.15);
  color: var(--color-text-white);
}


/* Form labels */
.form-label {
  color: var(--color-text-primary);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-sm);
  display: block;
  font-size: var(--font-size-sm);
  letter-spacing: 0.025em;
}

.form-label.required::after {
  content: ' *';
  color: var(--color-primary);
}

/* Form validation states */
.form-control.is-valid {
  border-color: #28a745;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.form-control.is-invalid {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.valid-feedback {
  color: #28a745;
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-xs);
}

.invalid-feedback {
  color: #dc3545;
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-xs);
}

/* Form help text */
.form-text {
  color: var(--color-text-tertiary);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-xs);
  line-height: var(--line-height-normal);
}

/* Checkbox and radio improvements */
.form-check {
  margin-bottom: var(--spacing-sm);
  padding-left: var(--spacing-xl);
  position: relative;
}

.form-check-input {
  width: 1.25rem;
  height: 1.25rem;
  margin-left: calc(-1 * var(--spacing-xl));
  margin-top: 0.125rem;
  background-color: rgba(255, 255, 255, 0.05);
  border: 2px solid var(--color-border);
  transition: all var(--transition-normal);
}

.form-check-input:checked {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.form-check-input:focus {
  box-shadow: 0 0 0 0.2rem var(--color-primary-alpha);
  outline: none;
}

.form-check-label {
  color: var(--color-text-primary);
  font-size: var(--font-size-sm);
  cursor: pointer;
}

/* Input groups */
.input-group {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  width: 100%;
}

.input-group-text {
  background-color: rgba(255, 255, 255, 0.05);
  border: 2px solid var(--color-border);
  color: var(--color-text-secondary);
  padding: var(--spacing-md);
  font-size: var(--font-size-base);
  border-radius: var(--radius-md);
}

/* Form spacing utilities */
.mb-3 {
  margin-bottom: var(--spacing-lg);
}

.mb-2 {
  margin-bottom: var(--spacing-md);
}

.mt-3 {
  margin-top: var(--spacing-lg);
}

/* Form responsiveness */
@media (max-width: 768px) {
  .form-control, .form-select {
    font-size: var(--font-size-base);
    padding: var(--spacing-md);
  }

  .form-label {
    font-size: var(--font-size-base);
  }
}

@media (max-width: 576px) {
  .form-control, .form-select {
    padding: var(--spacing-md);
    border-radius: var(--radius-lg);
  }

  textarea.form-control {
    min-height: 100px;
  }

  .form-check {
    padding-left: var(--spacing-lg);
  }

  .form-check-input {
    margin-left: calc(-1 * var(--spacing-lg));
  }
}

/* ===== TABLE STYLES ===== */


/* Smooth hover transitions for tables */
.table tbody tr,
.table td,
.table th {
  transition: background-color 0.3s ease-in-out,
              color 0.3s ease-in-out,
              border-color 0.3s ease-in-out;
}

.table {
  color: var(--color-text-primary);
  border-collapse: collapse;
  width: 100%;
}

.table th {
  background-color: var(--color-bg-secondary);
  color: var(--color-text-white);
  font-weight: var(--font-weight-semibold);
  padding: var(--spacing-md);
  border-bottom: 2px solid var(--color-primary);
}

.table td {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--color-border);
  vertical-align: middle;
}


/* Ensure table body matches site theme (admin apps) */
.table tbody tr {
  background-color: var(--color-bg-tertiary);
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(255, 255, 255, 0.02);
}

.table-striped tbody tr:hover {
  background-color: rgba(212, 175, 55, 0.1);
}

/* Table responsiveness */
@media (max-width: 768px) {
  .table {
    font-size: var(--font-size-sm);
  }

  .table th,
  .table td {
    padding: var(--spacing-sm);
  }
}


/* ===== ADMIN PANEL STYLES ===== */

/* Admin navigation */
.admin-nav {
  gap: var(--spacing-sm);
}

.admin-nav .btn {
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-md);
  transition: background-color var(--transition-normal),
              color var(--transition-normal),
              border-color var(--transition-normal),
              box-shadow var(--transition-normal),
              transform var(--transition-normal);
}

.admin-nav .btn:hover {
  transform: translateY(-1px);
}

/* Stat cards */
.stat-card {
  border-left: 4px solid var(--color-primary);
}

/* Email link styling */
.email-link {
  color: var(--color-primary);
  text-decoration: none;
  word-break: break-all;
}

.email-link:hover {
  color: var(--color-primary-light);
  text-decoration: underline;
}

/* Tier badges (admin usage) */
.tier-badge {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  text-transform: uppercase;
}

.tier-sentinel {
  background: linear-gradient(135deg, #cd7f32 0%, #b8860b 100%);
  color: #fff;
}

.tier-contributor {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
  color: var(--color-bg-primary);
}

.tier-patron {
  background: linear-gradient(135deg, #e5e4e2 0%, #c0c0c0 100%);
  color: var(--color-bg-primary);
}

/* Admin table theming (scoped to admin pages) */
.admin-dashboard .table,
.admin-members .table,
.admin-settings .table,
.admin-board-requests .table {
  background-color: transparent;
}

.admin-dashboard .table thead th,
.admin-members .table thead th,
.admin-settings .table thead th,
.admin-board-requests .table thead th {
  background-color: var(--color-bg-secondary);
  color: var(--color-text-white);
  border-bottom: 2px solid var(--color-primary);
}

.admin-dashboard .table tbody tr,
.admin-members .table tbody tr,
.admin-settings .table tbody tr,
.admin-board-requests .table tbody tr {
  background-color: var(--color-bg-tertiary);
  transition: background-color var(--transition-normal), color var(--transition-normal);
}

.admin-dashboard .table tbody td,
.admin-members .table tbody td,
.admin-settings .table tbody td,
.admin-board-requests .table tbody td {
  border-bottom: 1px solid var(--color-border);
  vertical-align: middle;
}

/* Striped and hover states */
.admin-dashboard .table-striped tbody tr:nth-of-type(odd),
.admin-members .table-striped tbody tr:nth-of-type(odd),
.admin-settings .table-striped tbody tr:nth-of-type(odd),
.admin-board-requests .table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(255, 255, 255, 0.03);
}

.admin-dashboard .table-striped tbody tr:hover,
.admin-members .table-striped tbody tr:hover,
.admin-settings .table-striped tbody tr:hover,
.admin-board-requests .table-striped tbody tr:hover {
  background-color: rgba(212, 175, 55, 0.08);
}

/* Application rows and reason text */
.application-row {
  transition: background-color var(--transition-normal);
}

.application-row:hover {
  background-color: rgba(212, 175, 55, 0.05);
}

.reason-text-container {
  max-width: 460px;
  min-width: 260px;
}

.reason-preview {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  line-height: 1.4;
  color: var(--color-text-secondary);
}

.reason-full-text {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  line-height: 1.5;
  color: var(--color-text-primary);
  background: var(--color-bg-secondary);
  padding: var(--spacing-sm);
  border-radius: var(--radius-sm);
  border-left: 3px solid var(--color-primary);
  white-space: pre-wrap;
}

/* Column sizing for admin tables */
.applications-section table thead th:nth-child(1),
.applications-section table tbody td:nth-child(1) { width: 20%; min-width: 180px; }
.applications-section table thead th:nth-child(2),
.applications-section table tbody td:nth-child(2) { width: 24%; min-width: 220px; word-break: break-all; }
.applications-section table thead th:nth-child(3),
.applications-section table tbody td:nth-child(3) { width: 12%; white-space: nowrap; }
.applications-section table thead th:nth-child(4),
.applications-section table tbody td:nth-child(4) { width: 32%; }
.applications-section table thead th:nth-child(5),
.applications-section table tbody td:nth-child(5) { width: 12%; min-width: 120px; }

.admin-members .col-user { width: 22%; min-width: 180px; }
.admin-members .col-email { width: 24%; min-width: 220px; word-break: break-all; }
.admin-members .col-tier { width: 10%; white-space: nowrap; }
.admin-members .col-status { width: 12%; white-space: nowrap; }
.admin-members .col-subscription { width: 18%; white-space: nowrap; }
.admin-members .col-actions { width: 14%; }

.admin-settings .col-name { width: 20%; min-width: 160px; }
.admin-settings .col-desc { width: 50%; }
.admin-settings .col-status { width: 10%; white-space: nowrap; }
.admin-settings .col-actions { width: 20%; }

.admin-board-requests .col-name { width: 18%; min-width: 160px; }
.admin-board-requests .col-user { width: 20%; min-width: 180px; }
.admin-board-requests .col-desc { width: 26%; }
.admin-board-requests .col-reason { width: 26%; }
.admin-board-requests .col-actions { width: 10%; min-width: 160px; }

/* Admin responsive tweaks */
@media (max-width: 1200px) {
  .reason-text-container { max-width: 250px; }
}

@media (max-width: 992px) {
  .table-responsive { font-size: var(--font-size-sm); }
  .reason-text-container { max-width: 200px; }
  .action-buttons { flex-direction: row; flex-wrap: wrap; }
}

@media (max-width: 768px) {
  .application-row td { padding: var(--spacing-sm); }
  .reason-text-container { max-width: 150px; }
  .tier-badge { font-size: 10px; padding: 2px 4px; }
}

/* ===== SECTION STYLES ===== */
section {
  padding: var(--spacing-4xl) var(--spacing-xl);
}

.section-alt {
  background-color: var(--color-bg-section-alt);
}

/* Section responsiveness */
@media (max-width: 768px) {
  section {
    padding: var(--spacing-2xl) var(--spacing-md);
  }
}

@media (max-width: 576px) {
  section {
    padding: var(--spacing-xl) var(--spacing-md);
  }
}

/* ===== FOOTER STYLES ===== */
footer {
  text-align: center;
  padding: var(--spacing-xl);
  border-top: 1px solid var(--color-border);
  color: var(--color-text-tertiary);
  font-size: var(--font-size-sm);
  background-color: var(--color-bg-primary);
}

/* ===== UTILITY CLASSES ===== */
.text-center {
  text-align: center;
}

.text-white {
  color: var(--color-text-white) !important;
}

.mt-3 {
  margin-top: var(--spacing-md);
}

.mt-5 {
  margin-top: var(--spacing-4xl);
}

.me-3 {
  margin-right: var(--spacing-md);
}

.ms-auto {
  margin-left: auto;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.container-fluid {
  width: 100%;
  padding: 0 var(--spacing-md);
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 calc(-1 * var(--spacing-sm));
}

.col-md-4 {
  flex: 0 0 auto;
  width: 33.33333333%;
  padding: 0 var(--spacing-sm);
}

.g-4 > * {
  margin-bottom: var(--spacing-lg);
}

/* Grid responsiveness */
@media (max-width: 768px) {
  .col-md-4 {
    width: 100%;
    margin-bottom: var(--spacing-lg);
  }

  .row {
    margin: 0;
  }

  .col-md-4 {
    padding: 0;
  }
}

/* ===== ACCESSIBILITY & COLOR STANDARDIZATION ===== */

/* WCAG 2.1 AA Compliant Color Scheme */
:root {
  /* Ensure all color combinations meet WCAG contrast requirements */
  --color-contrast-ratio-normal: 4.5; /* AA standard for normal text */
  --color-contrast-ratio-large: 3; /* AA standard for large text */

  /* Status colors with proper contrast */
  --color-success: #28a745;
  --color-success-light: #d4edda;
  --color-success-dark: #155724;

  --color-warning: #ffc107;
  --color-warning-light: #fff3cd;
  --color-warning-dark: #856404;

  --color-error: #dc3545;
  --color-error-light: #f8d7da;
  --color-error-dark: #721c24;

  --color-info: #17a2b8;
  --color-info-light: #d1ecf1;
  --color-info-dark: #0c5460;

  /* Focus indicators */
  --color-focus: var(--color-primary);
  --color-focus-shadow: var(--color-primary-alpha);
}

/* Enhanced accessibility features */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .hero-section::before {
    animation: none !important;
  }
}

/* Enhanced focus styles for better keyboard navigation */
*:focus {
  outline: 2px solid var(--color-focus);
  outline-offset: 2px;
  border-radius: 2px;
}

/* Specific focus styles for interactive elements */
button:focus,
.btn:focus,
a:focus,
input:focus,
select:focus,
textarea:focus,
.form-control:focus,
.form-select:focus,
.navbar-toggler:focus {
  outline: 2px solid var(--color-focus);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px var(--color-focus-shadow);
}

/* Skip to main content link for screen readers */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--color-primary);
  color: var(--color-bg-primary);
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 9999;
  font-weight: var(--font-weight-bold);
}

.skip-link:focus {
  top: 6px;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --color-primary: #ffdd44;
    --color-primary-light: #ffee77;
    --color-text-primary: #ffffff;
    --color-text-secondary: #ffffff;
    --color-bg-primary: #000000;
    --color-bg-secondary: #111111;
    --color-border: #ffffff;
  }

  .card, .lux-card {
    border-width: 3px;
    border-color: var(--color-primary);
  }

  .btn {
    border-width: 3px;
    font-weight: var(--font-weight-bold);
  }

  .navbar {
    border-bottom-width: 3px;
  }

  .form-control, .form-select {
    border-width: 3px;
  }
}

/* Color scheme consistency enforcement */
.text-primary {
  color: var(--color-primary) !important;
}

.text-secondary {
  color: var(--color-text-secondary) !important;
}

.text-success {
  color: var(--color-success) !important;
}

.text-warning {
  color: var(--color-warning) !important;
}

.text-danger {
  color: var(--color-error) !important;
}

.text-info {
  color: var(--color-info) !important;
}

.bg-primary {
  background-color: var(--color-primary) !important;
  color: var(--color-bg-primary) !important;
}

.bg-secondary {
  background-color: var(--color-bg-secondary) !important;
  color: var(--color-text-primary) !important;
}

.border-primary {
  border-color: var(--color-primary) !important;
}

/* Status message styling with proper contrast */
.alert-success {
  background-color: var(--color-success-light);
  border-color: var(--color-success);
  color: var(--color-success-dark);
}

.alert-warning {
  background-color: var(--color-warning-light);
  border-color: var(--color-warning);
  color: var(--color-warning-dark);
}

.alert-danger {
  background-color: var(--color-error-light);
  border-color: var(--color-error);
  color: var(--color-error-dark);
}

.alert-info {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--color-border-light);
  color: var(--color-text-primary);
  border-radius: var(--radius-xl);
  backdrop-filter: blur(6px);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

/* Button state colors for consistency */
.btn-success {
  background-color: var(--color-success);
  border-color: var(--color-success);
  color: white;
}

.btn-success:hover {
  background-color: var(--color-success-dark);
  border-color: var(--color-success-dark);
}

.btn-danger {
  background-color: var(--color-error);
  border-color: var(--color-error);
  color: white;
}

.btn-danger:hover {
  background-color: var(--color-error-dark);
  border-color: var(--color-error-dark);
}

.btn-warning {
  background-color: var(--color-warning);
  border-color: var(--color-warning);
  color: var(--color-warning-dark);
}

.btn-warning:hover {
  background-color: var(--color-warning-dark);
  border-color: var(--color-warning-dark);
  color: white;
}

/* Ensure proper color contrast for all text elements */
.navbar-text,
.card-header,
.table th,
.form-label {
  color: var(--color-text-primary);
}

.card-body,
.table td,
.form-text {
  color: var(--color-text-secondary);
}

/* Dark mode preference support (maintaining brand consistency) */
@media (prefers-color-scheme: light) {
  /* Users preferring light mode still get dark theme for brand consistency */
  /* But we can add subtle adjustments if needed */
  :root {
    --color-text-tertiary: #b0b0b0;
  }
}

/* Additional accessibility improvements */
.visually-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* Focus improvements for better accessibility */
.btn:focus-visible,
.form-control:focus-visible,
.form-select:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Improved text selection */
::selection {
  background-color: var(--color-primary-alpha-strong);
  color: var(--color-text-white);
}

::-moz-selection {
  background-color: var(--color-primary-alpha-strong);
  color: var(--color-text-white);
}

/* ===== INDEX PAGE SPECIFIC STYLES ===== */

/* Hero Section */
.hero-section {
  padding: var(--spacing-4xl) var(--spacing-xl);
  background: linear-gradient(135deg, var(--color-bg-primary) 0%, var(--color-bg-secondary) 100%);
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 70%, var(--color-primary-alpha-light) 0%, transparent 50%);
  pointer-events: none;
}

.hero-content {
  position: relative;
  z-index: 1;
}

.hero-title {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-lg);
  background: linear-gradient(135deg, var(--color-text-white) 0%, var(--color-primary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
  color: var(--color-primary);
  margin-bottom: var(--spacing-md);
}

.hero-description {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-relaxed);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-xl);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero-actions {
  margin-top: var(--spacing-xl);
}

/* Feature Items */
.feature-item {
  padding: var(--spacing-lg);
  text-align: center;
}

.feature-item h4 {
  color: var(--color-primary);
  margin-bottom: var(--spacing-sm);
  font-size: var(--font-size-lg);
}

.feature-item p {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

/* Enhanced Tier Cards with Smooth Transitions */
.tier-card {
  position: relative;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: 2px solid var(--color-primary-alpha);
  background: linear-gradient(145deg, var(--color-bg-tertiary), var(--color-bg-quaternary));
  overflow: hidden;
  cursor: pointer;
}

.tier-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.1), transparent);
  transition: left 0.6s ease;
  z-index: 1;
}

.tier-card:hover::before {
  left: 100%;
}

.tier-card:hover {
  transform: translateY(-15px) scale(1.03);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 0 50px rgba(212, 175, 55, 0.4);
  border-color: var(--color-primary);
}

.tier-card .card-body {
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

.tier-card:hover .card-body {
  transform: translateY(-2px);
}

.tier-badge {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.tier-badge.bronze {
  background: linear-gradient(135deg, #cd7f32 0%, #b8860b 100%);
  color: var(--color-text-white);
}

.tier-badge.gold {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
  color: var(--color-bg-primary);
}

.tier-badge.platinum {
  background: linear-gradient(135deg, #e5e4e2 0%, #c0c0c0 100%);
  color: var(--color-bg-primary);
}

.tier-title {
  color: var(--color-text-white);
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-xl);
}

.tier-description {
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-lg);
  line-height: var(--line-height-relaxed);
}

.tier-features {
  text-align: left;
  margin-bottom: var(--spacing-lg);
}

/* Enhanced Tier Card Elements */
.tier-header {
  border-bottom: 1px solid var(--color-primary-alpha);
  padding-bottom: var(--spacing-md);
}

.tier-price {
  margin: var(--spacing-md) 0;
}

.price-amount {
  font-size: 2.5rem;
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  font-family: var(--font-heading);
}

.price-period {
  font-size: var(--font-size-lg);
  color: var(--color-text-tertiary);
  font-weight: var(--font-weight-normal);
}

.features-title {
  color: var(--color-text-white);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-md);
  text-align: center;
}

.tier-features-section {
  text-align: left;
  padding: 0 var(--spacing-md);
}

.tier-features li {
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-sm);
  padding-left: var(--spacing-md);
  position: relative;
  line-height: var(--line-height-relaxed);
  transition: color 0.3s ease;
}

.tier-features li:hover {
  color: var(--color-text-primary);
}

.feature-icon {
  position: absolute;
  left: 0;
  top: 0;
  color: var(--color-primary);
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-sm);
}

.outcome-text {
  font-style: italic;
  color: var(--color-text-primary);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
  border-top: 1px solid var(--color-primary-alpha);
  padding-top: var(--spacing-md);
}

.outcome-text strong {
  color: var(--color-primary);
}

/* Featured Badge - Removed since no tier is "most popular" */

/* Tier-specific Button Styles */
.btn-tier-bronze {
  background: linear-gradient(135deg, #cd7f32 0%, #b8860b 100%);
  border: none;
  color: white;
  font-weight: var(--font-weight-semibold);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-tier-bronze::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn-tier-bronze:hover::before {
  left: 100%;
}

.btn-tier-bronze:hover {
  background: linear-gradient(135deg, #b8860b 0%, #cd7f32 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(205, 127, 50, 0.4);
}

.btn-tier-gold {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
  border: none;
  color: var(--color-bg-primary);
  font-weight: var(--font-weight-semibold);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-tier-gold::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.btn-tier-gold:hover::before {
  left: 100%;
}

.btn-tier-gold:hover {
  background: linear-gradient(135deg, var(--color-primary-light) 0%, var(--color-primary) 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(212, 175, 55, 0.5);
}

.btn-tier-platinum {
  background: linear-gradient(135deg, #e5e4e2 0%, #c0c0c0 100%);
  border: none;
  color: var(--color-bg-primary);
  font-weight: var(--font-weight-semibold);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-tier-platinum::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.btn-tier-platinum:hover::before {
  left: 100%;
}

.btn-tier-platinum:hover {
  background: linear-gradient(135deg, #c0c0c0 0%, #e5e4e2 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(192, 192, 192, 0.4);
}

/* Navigation Links */
.nav-link {
  color: var(--color-text-secondary) !important;
  font-weight: var(--font-weight-medium);
  transition: color var(--transition-normal);
  padding: var(--spacing-sm) var(--spacing-md) !important;
}

.nav-link:hover {
  color: var(--color-primary) !important;
}

/* Footer Links */
.footer-links {
  margin-bottom: var(--spacing-md);
}

.footer-links a {
  color: var(--color-text-tertiary);
  margin: 0 var(--spacing-sm);
  font-size: var(--font-size-sm);
  transition: color var(--transition-normal);
}

.footer-links a:hover {
  color: var(--color-primary);
}

/* Enhanced Responsive adjustments for index page */
@media (max-width: 992px) {
  .hero-section {
    padding: var(--spacing-3xl) var(--spacing-md);
  }

  .tier-card:hover {
    transform: translateY(-8px) scale(1.02);
  }

  .price-amount {
    font-size: 2rem;
  }

  .tier-features-section {
    padding: 0 var(--spacing-sm);
  }


}

@media (max-width: 768px) {
  .hero-title {
    font-size: var(--font-size-3xl);
  }

  .hero-subtitle {
    font-size: var(--font-size-lg);
  }

  .hero-description {
    font-size: var(--font-size-base);
  }

  .hero-actions .btn {
    display: block;
    width: 100%;
    margin-bottom: var(--spacing-sm);
  }

  .hero-actions .me-3 {
    margin-right: 0 !important;
  }

  /* Tier card mobile adjustments */
  .tier-card {
    margin-bottom: var(--spacing-xl);
  }

  .tier-card:hover {
    transform: translateY(-5px);
  }

  .price-amount {
    font-size: 1.8rem;
  }

  .tier-features-section {
    padding: 0;
    text-align: center;
  }

  .tier-features {
    text-align: left;
    display: inline-block;
  }


}

@media (max-width: 576px) {
  .hero-section {
    padding: var(--spacing-2xl) var(--spacing-md);
  }

  .hero-title {
    font-size: var(--font-size-2xl);
  }

  .tier-features {
    text-align: center;
  }

  .footer-links a {
    display: block;
    margin: var(--spacing-xs) 0;
  }
}

/* ===== CUSTOM SCROLLBARS ===== */

/* Webkit browsers (Chrome, Safari, Edge) */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-bg-secondary);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: var(--radius-sm);
  transition: background var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary-alpha);
}

::-webkit-scrollbar-thumb:active {
  background: var(--color-primary);
}

/* Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--color-border) var(--color-bg-secondary);
}

/* ===== RICH TEXT EDITOR STYLING ===== */

.ql-toolbar {
  background: var(--color-bg-quaternary) !important;
  border: 1px solid var(--color-border) !important;
  border-bottom: none !important;
  border-radius: var(--radius-md) var(--radius-md) 0 0 !important;
}

.ql-container {
  background: var(--color-bg-tertiary) !important;
  border: 1px solid var(--color-border) !important;
  border-radius: 0 0 var(--radius-md) var(--radius-md) !important;
  color: var(--color-text-primary) !important;
  font-family: var(--font-body) !important;
}

.ql-editor {
  color: var(--color-text-primary) !important;
  min-height: 120px;
  line-height: var(--line-height-relaxed);
}

.ql-editor.ql-blank::before {
  color: var(--color-text-tertiary) !important;
  font-style: normal;
}

.ql-toolbar .ql-stroke {
  stroke: var(--color-text-secondary) !important;
}

.ql-toolbar .ql-fill {
  fill: var(--color-text-secondary) !important;
}

.ql-toolbar .ql-picker-label {
  color: var(--color-text-secondary) !important;
}

.ql-toolbar button:hover .ql-stroke,
.ql-toolbar button.ql-active .ql-stroke {
  stroke: var(--color-primary) !important;
}

.ql-toolbar button:hover .ql-fill,
.ql-toolbar button.ql-active .ql-fill {
  fill: var(--color-primary) !important;
}

.ql-toolbar button:hover,
.ql-toolbar button.ql-active {
  background: var(--color-bg-secondary) !important;
  border-radius: var(--radius-sm);
}

.ql-picker-options {
  background: var(--color-bg-tertiary) !important;
  border: 1px solid var(--color-border) !important;
  border-radius: var(--radius-md) !important;
}

.ql-picker-item {
  color: var(--color-text-primary) !important;
}

.ql-picker-item:hover {
  background: var(--color-bg-secondary) !important;
}

/* ===== REDDIT-STYLE DISCUSSION BOARD ENHANCEMENTS ===== */

/* Modal improvements */
.modal-content {
  background: var(--color-bg-tertiary);
  border: 1px solid var(--color-border);
}

.modal-header {
  border-bottom: 1px solid var(--color-border);
  background: var(--color-bg-quaternary);
}

.modal-footer {
  border-top: 1px solid var(--color-border);
  background: var(--color-bg-quaternary);
}

/* Bootstrap Icons styling */

/* Unified post layout (used in board and post detail) */
.reddit-post {
  display: flex;
  background: var(--color-bg-tertiary);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  transition: all var(--transition-normal);
  overflow: hidden;
}

.post-vote-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-md);
  background: var(--color-bg-quaternary);
  border-right: 1px solid var(--color-border);
  min-width: 60px;
}

.post-content-section {
  flex: 1;
  padding: var(--spacing-md);
  min-width: 0; /* prevent overflow on long titles/content */
}

@media (max-width: 768px) {
  .reddit-post { flex-direction: column; }
  .post-vote-section {
    flex-direction: row;
    justify-content: center;
    border-right: none;
    border-bottom: 1px solid var(--color-border);
    min-width: auto;
    padding: var(--spacing-sm);
  }
}

.bi {
  font-size: inherit;
  line-height: 1;
}

/* Vote button enhancements */

/* Shared vote and action button styles */
.vote-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
}

.vote-btn {
  background: none;
  border: none;
  color: var(--color-text-tertiary);
  font-size: var(--font-size-lg);
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.vote-btn:hover {
  background: var(--color-bg-secondary);
  color: var(--color-text-primary);
}

.vote-btn.upvote.active {
  color: #ff4500;
  background: rgba(255, 69, 0, 0.1);
}

.vote-btn.downvote.active {
  color: #7193ff;
  background: rgba(113, 147, 255, 0.1);
}

.vote-btn.loading {
  opacity: 0.5;
  pointer-events: none;
}

.vote-score {
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  font-size: var(--font-size-sm);
  min-width: 20px;
  text-align: center;
}

.action-btn {
  background: none;
  border: none;
  color: var(--color-text-tertiary);
  font-size: var(--font-size-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.action-btn:hover {
  background: var(--color-bg-secondary);
  color: var(--color-text-primary);
}

.vote-btn .bi {
  font-size: 1.1em;
}

/* Action button improvements */
.action-btn .bi {
  font-size: 0.9em;
}

/* Board controls responsive improvements */
.board-controls {
  min-width: 0;
}

.board-controls .btn {
  white-space: nowrap;
}

/* Sort controls improvements */
.sort-controls .btn-group {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.sort-controls .btn-group .btn {
  border-radius: 0;
  border-right: 1px solid var(--color-border);

}

.sort-controls .btn-group .btn:last-child {
  border-right: none;
}

/* Post content improvements */
.post-content-section {
  min-width: 0;
}

.post-title h4 {
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.post-text {
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Comment improvements */
.comment-input {
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.comment-input:focus {
  background: var(--color-bg-primary);
}

/* Mobile-specific improvements */
@media (max-width: 992px) {
  .board-header .d-flex {
    gap: var(--spacing-lg) !important;
  }

  .board-controls {
    width: 100%;
    justify-content: space-between;
    flex-wrap: nowrap;
  }

  .sort-controls .btn-group .btn {


    font-size: var(--font-size-xs);
    padding: var(--spacing-xs);
  }
}

@media (max-width: 576px) {
  .board-title-section h2 {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-xs);
  }

  .board-title-section .lead {
    font-size: var(--font-size-sm);
    margin-bottom: 0;
  }

  .board-controls {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm) !important;
  }

  .sort-controls {
    width: 100%;
  }

  .sort-controls .btn-group {
    width: 100%;
    display: flex;
  }

  .sort-controls .btn-group .btn {
    flex: 1;
    text-align: center;
  }

  .modal-dialog {
    margin: var(--spacing-sm);
  }

  .modal-lg {


    max-width: none;
  }

/* Modal animation polish */
.modal.fade .modal-dialog {
  transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
  transform: translateY(-10px);
  opacity: 0;
}
.modal.show .modal-dialog {
  transform: translateY(0);
  opacity: 1;
}

/* ===== CUSTOM SCROLLBARS ===== */

/* Webkit browsers (Chrome, Safari, Edge) */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-bg-secondary);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: var(--radius-sm);
  transition: background var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary-alpha);
}

::-webkit-scrollbar-thumb:active {
  background: var(--color-primary);
}

::-webkit-scrollbar-corner {
  background: var(--color-bg-secondary);
}

/* Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--color-border) var(--color-bg-secondary);
}

/* Quill editor specific scrollbars */
.ql-container ::-webkit-scrollbar {
  width: 6px;
}

.ql-container ::-webkit-scrollbar-track {
  background: var(--color-bg-tertiary);
}

.ql-container ::-webkit-scrollbar-thumb {
  background: var(--color-border-light);
  border-radius: var(--radius-sm);
}

.ql-container ::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary-alpha);
}

/* Modal scrollbars */
.modal-body ::-webkit-scrollbar {
  width: 6px;
}

.modal-body ::-webkit-scrollbar-track {
  background: var(--color-bg-quaternary);
}

.modal-body ::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: var(--radius-sm);
}

/* ===== MODAL FIXES ===== */

/* Prevent modal hover issues */
.modal {
  pointer-events: none;
}

.modal.show {
  pointer-events: auto;
}

.modal-dialog {
  pointer-events: auto;
}

.modal-content {
  background: var(--color-bg-tertiary);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
  background: var(--color-bg-quaternary);
  border-bottom: 1px solid var(--color-border);
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
  padding: var(--spacing-lg);
}

.modal-title {
  color: var(--color-text-white);
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.modal-body {
  padding: var(--spacing-lg);
  background: var(--color-bg-tertiary);
}

.modal-footer {
  background: var(--color-bg-quaternary);
  border-top: 1px solid var(--color-border);
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
  padding: var(--spacing-lg);
  display: flex;
  gap: var(--spacing-sm);
  justify-content: flex-end;
}

.btn-close {
  background: none;
  border: none;
  color: var(--color-text-secondary);
  font-size: var(--font-size-lg);
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.btn-close:hover {
  background: var(--color-bg-secondary);
  color: var(--color-text-white);
}

/* Form styling in modals */
.modal .form-label {
  color: var(--color-text-white);
  font-weight: 500;
  margin-bottom: var(--spacing-xs);
}

.modal .form-label.required::after {
  content: " *";
  color: var(--color-danger);
}

.modal .form-control {
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  color: var(--color-text-primary);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm);
  transition: all var(--transition-fast);
}

.modal .form-control:focus {
  background: var(--color-bg-primary);
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px var(--color-primary-alpha);
  color: var(--color-text-white);
}

.modal .form-control.is-valid {
  border-color: var(--color-success);
}

.modal .form-control.is-invalid {
  border-color: var(--color-danger);
}

.modal .form-text {
  color: var(--color-text-tertiary);
  font-size: var(--font-size-xs);
  margin-top: var(--spacing-xs);
}

.modal .invalid-feedback {
  color: var(--color-danger);
  font-size: var(--font-size-xs);
  margin-top: var(--spacing-xs);
  display: none;
}

.modal .invalid-feedback[style*="block"] {
  display: block !important;
}

/* ===== QUILL EDITOR STYLING ===== */

/* Main editor containers */
.post-editor, .comment-editor, .reply-editor, .rich-text-editor {
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  overflow: hidden;
  transition: border-color var(--transition-fast);
}

.post-editor:focus-within,
.comment-editor:focus-within,
.reply-editor:focus-within,
.rich-text-editor:focus-within {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px var(--color-primary-alpha);
}

/* Quill toolbar styling */
.ql-toolbar {
  background: var(--color-bg-quaternary) !important;
  border-bottom: 1px solid var(--color-border) !important;
  border-radius: var(--radius-md) var(--radius-md) 0 0 !important;
  padding: var(--spacing-sm) !important;
}

.ql-toolbar .ql-formats {
  margin-right: var(--spacing-sm);
}

.ql-toolbar button {
  color: var(--color-text-secondary) !important;
  border-radius: var(--radius-xs) !important;
  padding: var(--spacing-xs) !important;
  margin: 0 2px !important;
  transition: all var(--transition-fast) !important;
}

.ql-toolbar button:hover {
  background: var(--color-bg-tertiary) !important;
  color: var(--color-primary) !important;
}

.ql-toolbar button.ql-active {
  background: var(--color-primary-alpha) !important;
  color: var(--color-primary) !important;
}

.ql-toolbar .ql-stroke {
  stroke: var(--color-text-secondary) !important;
  transition: stroke var(--transition-fast) !important;
}

.ql-toolbar .ql-fill {
  fill: var(--color-text-secondary) !important;
  transition: fill var(--transition-fast) !important;
}

.ql-toolbar button:hover .ql-stroke,
.ql-toolbar button.ql-active .ql-stroke {
  stroke: var(--color-primary) !important;
}

.ql-toolbar button:hover .ql-fill,
.ql-toolbar button.ql-active .ql-fill {
  fill: var(--color-primary) !important;
}

/* Quill editor content area */
.ql-container {
  background: var(--color-bg-secondary) !important;
  color: var(--color-text-primary) !important;
  border-radius: 0 0 var(--radius-md) var(--radius-md) !important;
  font-family: var(--font-family-primary) !important;
  font-size: var(--font-size-sm) !important;
}

.ql-editor {
  color: var(--color-text-primary) !important;
  line-height: var(--line-height-relaxed) !important;
  padding: var(--spacing-md) !important;
  min-height: 120px;
}

.ql-editor.ql-blank::before {
  color: var(--color-text-tertiary) !important;
  font-style: normal !important;
  opacity: 0.7;
}

.ql-editor p {
  margin-bottom: var(--spacing-sm);
}

.ql-editor h1, .ql-editor h2, .ql-editor h3 {
  color: var(--color-text-white);
  margin: var(--spacing-md) 0 var(--spacing-sm) 0;
}

.ql-editor blockquote {
  border-left: 3px solid var(--color-primary);
  background: var(--color-bg-tertiary);
  padding: var(--spacing-sm) var(--spacing-md);
  margin: var(--spacing-md) 0;
  border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
}

.ql-editor code {
  background: var(--color-bg-tertiary);
  color: var(--color-primary);
  padding: 2px 4px;
  border-radius: var(--radius-xs);
  font-family: 'Courier New', monospace;
}

.ql-editor pre {
  background: var(--color-bg-tertiary);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-sm);
  padding: var(--spacing-md);
  overflow-x: auto;
  margin: var(--spacing-md) 0;
}

.ql-editor a {
  color: var(--color-primary);
  text-decoration: none;
}

.ql-editor a:hover {
  text-decoration: underline;
}

/* Reply editor specific styling */
.reply-editor .ql-editor {
  min-height: 80px;
}

.reply-editor .ql-toolbar {
  padding: var(--spacing-xs) var(--spacing-sm) !important;
}

/* Responsive Quill editor */
@media (max-width: 768px) {
  .ql-toolbar {
    padding: var(--spacing-xs) !important;
  }

  .ql-toolbar .ql-formats {
    margin-right: var(--spacing-xs);
  }

  .ql-toolbar button {
    padding: 2px !important;
    margin: 0 1px !important;
  }

  .ql-editor {
    padding: var(--spacing-sm) !important;
    min-height: 100px;
  }

  .reply-editor .ql-editor {
    min-height: 60px;
  }
}

/* ===== POST DETAIL PAGE FIXES ===== */

.post-detail-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.breadcrumb-nav .btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.breadcrumb-nav .btn:hover {
  background-color: var(--color-primary-alpha);
  border-color: var(--color-primary);
  color: var(--color-primary);
}

.main-post {
  background: var(--color-bg-tertiary);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  overflow: hidden;
  margin-bottom: var(--spacing-xl);
}

.main-post .post-title h1 {
  font-size: var(--font-size-2xl);
  font-weight: 600;
  color: var(--color-text-white);
  margin: var(--spacing-md) 0;
  line-height: var(--line-height-tight);
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.main-post .post-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  flex-wrap: wrap;
  font-size: var(--font-size-sm);
  color: var(--color-text-tertiary);
  margin-bottom: var(--spacing-sm);
}

.main-post .post-board {
  color: var(--color-primary);
  font-weight: 500;
}

.main-post .username {
  color: var(--color-text-secondary);
  text-decoration: none;
  font-weight: 500;
}

.main-post .username:hover {
  color: var(--color-primary);
  text-decoration: underline;
}

.main-post .op-badge {
  background: var(--color-primary);
  color: var(--color-bg-primary);
  padding: 2px 6px;
  border-radius: var(--radius-xs);
  font-size: var(--font-size-xs);
  font-weight: 600;
  margin-left: var(--spacing-xs);
}

.main-post .post-text {
  color: var(--color-text-primary);
  line-height: var(--line-height-relaxed);
  margin: var(--spacing-md) 0;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.main-post .post-text p {
  margin-bottom: var(--spacing-sm);
}

.main-post .post-text h1,
.main-post .post-text h2,
.main-post .post-text h3 {
  color: var(--color-text-white);
  margin: var(--spacing-lg) 0 var(--spacing-sm) 0;
}

.main-post .post-text blockquote {
  border-left: 3px solid var(--color-primary);
  background: var(--color-bg-quaternary);
  padding: var(--spacing-sm) var(--spacing-md);
  margin: var(--spacing-md) 0;
  border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
}

.main-post .post-text code {
  background: var(--color-bg-quaternary);
  color: var(--color-primary);
  padding: 2px 4px;
  border-radius: var(--radius-xs);
  font-family: 'Courier New', monospace;
}

.main-post .post-text pre {
  background: var(--color-bg-quaternary);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-sm);
  padding: var(--spacing-md);
  overflow-x: auto;
  margin: var(--spacing-md) 0;
}

.main-post .post-text a {
  color: var(--color-primary);
  text-decoration: none;
}

.main-post .post-text a:hover {
  text-decoration: underline;
}

.main-comment-form {
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--color-border);
}

.comments-section {
  margin-top: var(--spacing-xl);
}

.comments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--color-border);
}

.comments-header h3 {
  color: var(--color-text-white);
  font-size: var(--font-size-xl);
  margin: 0;
}

.sort-comments .form-select {
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  color: var(--color-text-primary);
  font-size: var(--font-size-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
}

.sort-comments .form-select:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px var(--color-primary-alpha);
}

.no-comments {
  background: var(--color-bg-tertiary);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  padding: var(--spacing-xl);
  text-align: center;
}

.no-comments p {
  color: var(--color-text-tertiary);
  font-size: var(--font-size-lg);
  margin: 0;
}

/* ===== POST DETAIL RESPONSIVE DESIGN ===== */

/* Responsive design */
@media (max-width: 992px) {
  .post-detail-page {
    padding: 0 var(--spacing-md);
  }

  .main-post .post-title h1 {
    font-size: var(--font-size-xl);
  }

  .comments-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .sort-comments {
    width: 100%;
  }

  .sort-comments .form-select {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .main-post, .comment-item {
    flex-direction: column;
  }

  .post-vote-section, .comment-vote-section {
    flex-direction: row;
    justify-content: center;
    border-right: none;
    border-bottom: 1px solid var(--color-border);
    min-width: auto;
    padding: var(--spacing-sm);
  }

  .vote-buttons {
    flex-direction: row;
    gap: var(--spacing-md);
    align-items: center;
  }

  .comment-content-section {
    padding: var(--spacing-sm);
  }

  .comment-meta {
    font-size: var(--font-size-xs);
  }

  .main-post .post-title h1 {
    font-size: var(--font-size-lg);
  }

  .main-post .post-meta {
    font-size: var(--font-size-xs);
  }

  .breadcrumb-nav .btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
  }
}

@media (max-width: 576px) {
  .post-detail-page {
    padding: 0 var(--spacing-sm);
  }

  .comment-replies {
    margin-left: var(--spacing-sm);
    padding-left: var(--spacing-sm);
  }

  .comment-thread[style*="margin-left"] {
    margin-left: var(--spacing-sm) !important;
  }

  .main-post .post-title h1 {
    font-size: var(--font-size-md);
    line-height: var(--line-height-normal);
  }

  .main-post .post-content-section {
    padding: var(--spacing-sm);
  }

  .comments-header h3 {
    font-size: var(--font-size-lg);
  }

  .modal-dialog {
    margin: var(--spacing-sm);
  }

  .modal-lg {
    max-width: none;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: var(--spacing-md);
  }
}

/* ===== ENHANCED ANIMATIONS AND MICRO-INTERACTIONS ===== */

/* Removed floating animation since no tier is featured */

/* Staggered animation for tier cards on load */
.tier-card:nth-child(1) {
  animation-delay: 0.1s;
}

.tier-card:nth-child(2) {
  animation-delay: 0.2s;
}

.tier-card:nth-child(3) {
  animation-delay: 0.3s;
}

/* Enhanced button hover effects */
.btn-tier-bronze,
.btn-tier-gold,
.btn-tier-platinum {
  transform-origin: center;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.btn-tier-bronze:active,
.btn-tier-gold:active,
.btn-tier-platinum:active {
  transform: scale(0.95);
}

/* Smooth price number animation */
.price-amount {
  transition: all 0.3s ease;
}

.tier-card:hover .price-amount {
  transform: scale(1.1);
  text-shadow: 0 0 20px rgba(212, 175, 55, 0.5);
}

/* Feature list hover effects */
.tier-features li {
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
  padding-left: calc(var(--spacing-md) + 3px);
  margin-left: -3px;
}

.tier-features li:hover {
  border-left-color: var(--color-primary);
  padding-left: calc(var(--spacing-md) + 6px);
  background: rgba(212, 175, 55, 0.05);
}

/* Tier badge pulse effect */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(212, 175, 55, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(212, 175, 55, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(212, 175, 55, 0);
  }
}

.tier-badge {
  transition: all 0.3s ease;
}

.tier-card:hover .tier-badge {
  animation: pulse 2s infinite;
  transform: scale(1.05);
}

/* Smooth section transitions */
.tiers-section {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s ease forwards;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced focus states for accessibility */
.tier-card:focus-within {
  outline: 3px solid var(--color-primary);
  outline-offset: 5px;
}

.btn-tier-bronze:focus,
.btn-tier-gold:focus,
.btn-tier-platinum:focus {
  outline: 3px solid var(--color-primary);
  outline-offset: 3px;
  box-shadow: 0 0 0 6px rgba(212, 175, 55, 0.2);
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  .tier-card:hover .tier-badge {
    animation: none;
  }

  .tier-card,
  .tier-features li,
  .price-amount,
  .btn-tier-bronze,
  .btn-tier-gold,
  .btn-tier-platinum {
    transition: none;
  }
}

/* ===== ENHANCED RESPONSIVE DESIGN ===== */

/* Touch-friendly interactions */
@media (hover: none) and (pointer: coarse) {
  .btn, .card, .lux-card {
    transform: none !important;
  }

  .btn:active {
    transform: scale(0.98);
  }

  .card:active, .lux-card:active {
    transform: scale(0.98);
  }

  /* Larger touch targets */
  .btn {
    min-height: 44px;
    min-width: 44px;
  }

  .navbar-toggler {
    min-height: 44px;
    min-width: 44px;
  }

  .nav-link {
    min-height: 44px;
    display: flex;
    align-items: center;
  }
}

/* Extra small devices (portrait phones, less than 576px) */
@media (max-width: 575.98px) {
  :root {
    --spacing-xs: 0.125rem;
    --spacing-sm: 0.375rem;
    --spacing-md: 0.75rem;
    --spacing-lg: 1.125rem;
    --spacing-xl: 1.5rem;
    --spacing-2xl: 2rem;
    --spacing-3xl: 2.5rem;
    --spacing-4xl: 3rem;
  }

  .container {
    padding: 0 var(--spacing-sm);
  }

  .navbar {
    padding: var(--spacing-sm);
  }

  .lux-card, .card {
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-md);
  }

  .table-responsive {
    font-size: var(--font-size-xs);
  }

  .btn-lg {
    font-size: var(--font-size-sm);
    padding: var(--spacing-sm) var(--spacing-md);
  }
}

/* Small devices (landscape phones, 576px and up) */
@media (min-width: 576px) and (max-width: 767.98px) {
  .container {
    max-width: 540px;
  }

  .col-sm-6 {
    width: 50%;
  }

  .col-sm-12 {
    width: 100%;
  }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) and (max-width: 991.98px) {
  .container {
    max-width: 720px;
  }

  .navbar {
    padding: var(--spacing-md) var(--spacing-lg);
  }

  section {
    padding: var(--spacing-3xl) var(--spacing-lg);
  }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) and (max-width: 1199.98px) {
  .container {
    max-width: 960px;
  }
}

/* Extra large devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
  .container {
    max-width: 1140px;
  }

  .hero-title {
    font-size: 3.5rem;
  }

  .hero-subtitle {
    font-size: 1.5rem;
  }
}

/* Ultra-wide screens (1400px and up) */
@media (min-width: 1400px) {
  .container {
    max-width: 1320px;
  }
}

/* Landscape orientation adjustments */
@media (orientation: landscape) and (max-height: 600px) {
  .hero-section {
    padding: var(--spacing-xl) var(--spacing-md);
  }

  .navbar {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  section {
    padding: var(--spacing-xl) var(--spacing-md);
  }
}

/* Print styles */
@media print {
  .navbar, .btn, .hero-actions, .apply-actions {
    display: none !important;
  }

  body {
    background: white !important;
    color: black !important;
  }

  .card, .lux-card {
    border: 1px solid #ccc !important;
    box-shadow: none !important;
    background: white !important;
  }

  h1, h2, h3, h4, h5, h6 {
    color: black !important;
  }

  a {
    color: black !important;
    text-decoration: underline !important;
  }
}

/* Dark mode preference support */
@media (prefers-color-scheme: light) {
  /* Users who prefer light mode can be accommodated if needed */
  /* Currently maintaining dark theme as it's part of the brand */
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .hero-section::before {
    animation: none;
  }

  .card, .lux-card, .btn {
    transition: none !important;
  }

  html {
    scroll-behavior: auto;
  }
}

/* High contrast mode improvements */
@media (prefers-contrast: high) {
  .card, .lux-card {
    border-width: 2px;
  }

  .btn {
    border-width: 2px;
  }

  .navbar {
    border-bottom-width: 2px;
  }
}
