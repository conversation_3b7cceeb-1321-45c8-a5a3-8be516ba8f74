{% extends "layout.html" %}
{% block content %}
<div class="registration-form">
  <div class="text-center mb-4">
    <h2>Complete Your Registration</h2>
    <p class="lead">You're almost there! Complete your account setup</p>
    <p>Use the invite code you received to finalize your Vallum membership.</p>
  </div>

  <form method="POST" class="needs-validation" novalidate>
    <div class="row">
      <div class="col-md-6">
        <div class="form-group">
          <label for="email" class="form-label required">Email Address</label>
          <input type="email" class="form-control" id="email" name="email" placeholder="<EMAIL>" required autocomplete="email">
          <div class="invalid-feedback">
            Please provide a valid email address.
          </div>
          <div class="form-text">Must match the email from your application.</div>
        </div>
      </div>

      <div class="col-md-6">
        <div class="form-group">
          <label for="username" class="form-label required">Username</label>
          <input type="text" class="form-control" id="username" name="username" placeholder="Choose your username" required autocomplete="username">
          <div class="invalid-feedback">
            Please choose a username.
          </div>
          <div class="form-text">This will be your display name in the community.</div>
        </div>
      </div>
    </div>

    <div class="form-group">
      <label for="password" class="form-label required">Password</label>
      <input type="password" class="form-control" id="password" name="password" placeholder="Create a secure password" required autocomplete="new-password" minlength="8">
      <div class="invalid-feedback">
        Password must be at least 8 characters long.
      </div>
      <div class="form-text">Choose a strong password with at least 8 characters.</div>
    </div>

    <div class="form-group">
      <label for="invite_code" class="form-label required">Invite Code</label>
      <input type="text" class="form-control" id="invite_code" name="invite_code" placeholder="Enter your invite code" required style="font-family: monospace; letter-spacing: 2px;">
      <div class="invalid-feedback">
        Please enter your invite code.
      </div>
      <div class="form-text">This was provided in your approval email.</div>
    </div>

    <div class="form-group">
      <div class="form-check">
        <input class="form-check-input" type="checkbox" id="terms" required>
        <label class="form-check-label" for="terms">
          I confirm that I agree to the community guidelines and terms of service.
        </label>
        <div class="invalid-feedback">
          You must agree to the terms to complete registration.
        </div>
      </div>
    </div>

    <div class="text-center">
      <button type="submit" class="btn btn-success btn-lg w-100 mb-3">
        Complete Registration
      </button>

      <div class="registration-links">
        <p class="mb-0">
          <small class="text-muted">
            Already have an account? <a href="/login">Sign in here</a>
          </small>
        </p>
      </div>
    </div>
  </form>
</div>

<script>
// Enhanced form validation with password strength indicator
(function() {
  'use strict';

  const form = document.querySelector('.needs-validation');
  const passwordInput = document.getElementById('password');

  // Password strength indicator
  function checkPasswordStrength(password) {
    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    return strength;
  }

  passwordInput.addEventListener('input', function() {
    const strength = checkPasswordStrength(this.value);
    const strengthText = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'][strength];
    const strengthColors = ['#dc3545', '#fd7e14', '#ffc107', '#28a745', '#20c997'];

    let strengthIndicator = document.querySelector('.password-strength');
    if (!strengthIndicator) {
      strengthIndicator = document.createElement('div');
      strengthIndicator.className = 'password-strength form-text';
      this.parentNode.appendChild(strengthIndicator);
    }

    if (this.value.length > 0) {
      strengthIndicator.innerHTML = `Password strength: <span style="color: ${strengthColors[strength]}">${strengthText}</span>`;
    } else {
      strengthIndicator.innerHTML = '';
    }
  });

  form.addEventListener('submit', function(event) {
    if (!form.checkValidity()) {
      event.preventDefault();
      event.stopPropagation();
    }

    form.classList.add('was-validated');
  }, false);

  // Real-time validation feedback
  const inputs = form.querySelectorAll('input');
  inputs.forEach(input => {
    input.addEventListener('blur', function() {
      if (this.checkValidity()) {
        this.classList.remove('is-invalid');
        this.classList.add('is-valid');
      } else {
        this.classList.remove('is-valid');
        this.classList.add('is-invalid');
      }
    });
  });
})();
</script>
{% endblock %}
