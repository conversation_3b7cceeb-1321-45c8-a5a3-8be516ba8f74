{% extends "layout.html" %}
{% block content %}
<div class="admin-board-requests">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <div>
      <h2 class="mb-1">Board Requests</h2>
      <p class="text-muted mb-0">Review and approve or deny user requests for new boards</p>
    </div>
    <div class="d-flex gap-2">
      <a href="/admin/applications" class="btn btn-outline-secondary">Applications</a>
      <a href="/admin/board-requests" class="btn btn-primary">Board Requests</a>
      <a href="/admin/members" class="btn btn-outline-secondary">Members</a>
      <a href="/admin/settings/boards" class="btn btn-outline-secondary">Boards</a>
    </div>
  </div>

  {% if requests %}
  <div class="table-responsive">
    <table class="table table-striped align-middle">
      <thead>
        <tr>
          <th scope="col">Proposed Board</th>
          <th scope="col">Requested By</th>
          <th scope="col">Description</th>
          <th scope="col">Reason</th>
          <th scope="col">Actions</th>
        </tr>
      </thead>
      <tbody>
        {% for r in requests %}
        <tr>
          <td class="col-name"><strong class="text-white">{{ r.name }}</strong></td>
          <td class="col-user">
            {% set u = user_map.get(r.user_id) %}
            {% if u %}
              <div class="d-flex flex-column">
                <strong class="text-white">{{ u.username }}</strong>
                <small class="text-muted">{{ u.email }}</small>
              </div>
            {% else %}
              <span class="text-muted">User #{{ r.user_id }}</span>
            {% endif %}
          </td>
          <td class="col-desc"><span class="text-muted">{{ r.description or '—' }}</span></td>
          <td class="col-reason"><span class="text-muted">{{ r.reason or '—' }}</span></td>
          <td class="col-actions">
            <div class="d-flex gap-2 flex-wrap">
              <a href="{{ url_for('admin.approve_board_request', req_id=r.id) }}" class="btn btn-sm btn-success" onclick="return confirm('Approve board request for {{ r.name }}? This will create the board.');">Approve</a>
              <a href="{{ url_for('admin.deny_board_request', req_id=r.id) }}" class="btn btn-sm btn-danger" onclick="return confirm('Deny this board request?');">Deny</a>
            </div>
          </td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
  {% else %}
    <div class="empty-state text-center py-5">
      <div class="empty-icon mb-3">
        <i class="bi bi-layout-text-window" style="font-size: 3rem; opacity: 0.3; color: var(--color-text-tertiary);"></i>
      </div>
      <h4>No pending board requests</h4>
      <p class="text-muted">New requests will appear here.</p>
    </div>
  {% endif %}
</div>

<style>
.admin-board-requests .table thead th {
  background-color: var(--color-bg-secondary);
  color: var(--color-text-white);
  border-bottom: 2px solid var(--color-primary);
}
.admin-board-requests .table tbody td { border-bottom: 1px solid var(--color-border); }
.admin-board-requests .col-name { width: 18%; min-width: 160px; }
.admin-board-requests .col-user { width: 20%; min-width: 180px; }
.admin-board-requests .col-desc { width: 26%; }
.admin-board-requests .col-reason { width: 26%; }
.admin-board-requests .col-actions { width: 10%; min-width: 160px; }
</style>
{% endblock %}

