{% extends "layout.html" %}

{% macro render_vote_buttons(item, item_type) %}
<div class="vote-buttons" data-{{ item_type }}-id="{{ item.id }}">
  {% set user_vote = item.get_user_vote(current_user.id) if current_user.is_authenticated else None %}
  <button class="vote-btn upvote {{ 'active' if user_vote and user_vote.vote_type == 'upvote' else '' }}"
          data-vote-type="upvote" data-{{ item_type }}-id="{{ item.id }}">
    <i class="bi bi-arrow-up"></i>
  </button>
  <span class="vote-score" data-{{ item_type }}-id="{{ item.id }}">{{ item.vote_score }}</span>
  <button class="vote-btn downvote {{ 'active' if user_vote and user_vote.vote_type == 'downvote' else '' }}"
          data-vote-type="downvote" data-{{ item_type }}-id="{{ item.id }}">
    <i class="bi bi-arrow-down"></i>
  </button>
</div>
{% endmacro %}

{% macro render_comment(comment, depth=0) %}
<div class="comment-thread" data-comment-id="{{ comment.id }}" style="margin-left: {{ depth * 20 }}px;">
  <div class="comment-item">
    <div class="comment-vote-section">
      {{ render_vote_buttons(comment, 'comment') }}
    </div>
    
    <div class="comment-content-section">
      <div class="comment-header">
        <div class="comment-meta">
          <a href="#" class="username">u/{{ comment.author.username }}</a>
          {% if comment.author_id == post.author_id %}<span class="op-badge">OP</span>{% endif %}
          <span class="comment-separator">•</span>
          <span class="comment-time" title="{{ comment.created_at.strftime('%B %d, %Y at %I:%M %p') }}">
            {{ time_ago(comment.created_at) }}
          </span>
        </div>
      </div>
      
      <div class="comment-body">
        <div class="comment-text">
          {{ comment.content | safe }}
        </div>
      </div>
      
      <div class="comment-footer">
        <div class="comment-actions">
          <button class="action-btn reply-btn" onclick="toggleReplyForm({{ comment.id }})">
            <i class="bi bi-reply"></i>
            <span>Reply</span>
          </button>

          <button class="action-btn share-btn">
            <i class="bi bi-share"></i>
            <span>Share</span>
          </button>
        </div>
      </div>

      <!-- Reply Form -->
      <div id="reply-form-{{ comment.id }}" class="reply-form" style="display: none;">
        <form method="POST" action="{{ url_for('dash.create_comment') }}" class="comment-form">
          <input type="hidden" name="post_id" value="{{ post.id }}">
          <input type="hidden" name="parent_id" value="{{ comment.id }}">
          <div class="comment-input-wrapper">
            <div id="reply-editor-{{ comment.id }}" class="reply-editor"></div>
            <input type="hidden" name="content" id="reply-content-{{ comment.id }}">
          </div>
          <div class="comment-actions">
            <button type="button" class="btn btn-sm btn-secondary" onclick="toggleReplyForm({{ comment.id }})">
              <i class="bi bi-x-circle me-1"></i>Cancel
            </button>
            <button type="submit" class="btn btn-sm btn-primary">
              <i class="bi bi-reply me-1"></i>Reply
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
  
  <!-- Nested replies -->
  {% if comment.replies and depth < 3 %}
    <div class="comment-replies">
      {% for reply in comment.replies | sort(attribute='vote_score', reverse=true) %}
        {{ render_comment(reply, depth + 1) }}
      {% endfor %}
    </div>
  {% endif %}
</div>
{% endmacro %}

{% block content %}
<div class="post-detail-page">
  <!-- Back to board link -->
  <div class="breadcrumb-nav mb-4">
    <a href="{{ url_for('dash.dashboard') }}" class="btn btn-outline-secondary">
      <i class="bi bi-arrow-left me-2"></i>Back to Board
    </a>
  </div>

  <!-- Main Post -->
  <article class="reddit-post main-post mb-4">
    <div class="post-vote-section">
      {{ render_vote_buttons(post, 'post') }}
    </div>

    <div class="post-content-section">
      <div class="post-header">
        <div class="post-meta">
          <span class="post-board">v/{{ post.board }}</span>
          <span class="post-separator">•</span>
          <span class="post-author">
            Posted by
            <a href="#" class="username">u/{{ post.author.username }}</a>
            <span class="op-badge">OP</span>
          </span>
          <span class="post-separator">•</span>
          <span class="post-time" title="{{ post.created_at.strftime('%B %d, %Y at %I:%M %p') }}">
            {{ time_ago(post.created_at) }}
          </span>
        </div>
      </div>

      <div class="post-title">
        <h1>{{ post.title }}</h1>
      </div>

      <div class="post-body">
        <div class="post-text">
          {{ post.content | safe }}
        </div>
      </div>

      <div class="post-footer">
        <div class="post-actions">
          <button class="action-btn comment-btn" onclick="toggleMainCommentForm()">
            <i class="bi bi-chat"></i>
            <span>{{ post.comment_count }} comment{{ 's' if post.comment_count != 1 else '' }}</span>
          </button>

          <button class="action-btn share-btn">
            <i class="bi bi-share"></i>
            <span>Share</span>
          </button>

          <button class="action-btn save-btn">
            <i class="bi bi-bookmark"></i>
            <span>Save</span>
          </button>
        </div>
      </div>

      <!-- Main Comment Form -->
      <div id="main-comment-form" class="main-comment-form">
        <form method="POST" action="{{ url_for('dash.create_comment') }}" class="comment-form">
          <input type="hidden" name="post_id" value="{{ post.id }}">
          <div class="comment-input-wrapper">
            <div id="comment-editor" class="comment-editor"></div>
            <input type="hidden" name="content" id="comment-content">
          </div>
          <div class="comment-actions">
            <button type="submit" class="btn btn-primary">
              <i class="bi bi-send me-2"></i>Comment
            </button>
          </div>
        </form>
      </div>
    </div>
  </article>

  <!-- Comments Section -->
  <div class="comments-section">
    <div class="comments-header">
      <h3>Comments ({{ comments | length }})</h3>
      <div class="sort-comments">
        <select class="form-select form-select-sm">
          <option value="best">Best</option>
          <option value="top">Top</option>
          <option value="new">New</option>
          <option value="controversial">Controversial</option>
        </select>
      </div>
    </div>
    
    {% if comments %}
      <div class="comments-list">
        {% for comment in comments %}
          {{ render_comment(comment) }}
        {% endfor %}
      </div>
    {% else %}
      <div class="no-comments text-center py-4">
        <p class="text-muted">No comments yet. Be the first to share your thoughts!</p>
      </div>
    {% endif %}
  </div>
</div>

<script>
// Voting functionality (same as board.html)
function handleVote(event) {
  event.preventDefault();
  
  const button = event.currentTarget;
  const voteType = button.dataset.voteType;
  const postId = button.dataset.postId;
  const commentId = button.dataset.commentId;
  
  button.classList.add('loading');
  
  const data = { vote_type: voteType };
  if (postId) data.post_id = parseInt(postId);
  if (commentId) data.comment_id = parseInt(commentId);
  
  fetch('{{ url_for("dash.vote") }}', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      const targetId = postId || commentId;
      const targetType = postId ? 'post' : 'comment';
      const scoreElement = document.querySelector(`[data-${targetType}-id="${targetId}"] .vote-score`);
      if (scoreElement) scoreElement.textContent = data.vote_score;
      
      const voteContainer = button.closest('.vote-buttons');
      const upvoteBtn = voteContainer.querySelector('.upvote');
      const downvoteBtn = voteContainer.querySelector('.downvote');
      
      upvoteBtn.classList.remove('active');
      downvoteBtn.classList.remove('active');
      
      if (data.action !== 'removed') {
        if (data.vote_type === 'upvote') upvoteBtn.classList.add('active');
        else if (data.vote_type === 'downvote') downvoteBtn.classList.add('active');
      }
    }
  })
  .catch(error => console.error('Error:', error))
  .finally(() => button.classList.remove('loading'));
}

// Rich text editors
let commentEditor;
let replyEditors = {};

// Initialize Quill editors
function initializeEditors() {
  // Main comment editor
  commentEditor = new Quill('#comment-editor', {
    theme: 'snow',
    placeholder: 'What are your thoughts?',
    modules: {
      toolbar: [
        ['bold', 'italic', 'underline'],
        ['link', 'blockquote', 'code-block'],
        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
        ['clean']
      ]
    }
  });

  // Style the editor
  const commentEditorContainer = document.querySelector('#comment-editor .ql-container');
  if (commentEditorContainer) {
    commentEditorContainer.style.minHeight = '120px';
  }
}

// Toggle reply forms
function toggleReplyForm(commentId) {
  const form = document.getElementById(`reply-form-${commentId}`);
  if (form.style.display === 'none' || !form.style.display) {
    form.style.display = 'block';

    // Initialize reply editor if not already done
    if (!replyEditors[commentId]) {
      replyEditors[commentId] = new Quill(`#reply-editor-${commentId}`, {
        theme: 'snow',
        placeholder: 'Write a reply...',
        modules: {
          toolbar: [
            ['bold', 'italic'],
            ['link', 'blockquote'],
            [{ 'list': 'ordered'}, { 'list': 'bullet' }]
          ]
        }
      });

      // Style the reply editor
      const replyEditorContainer = document.querySelector(`#reply-editor-${commentId} .ql-container`);
      if (replyEditorContainer) {
        replyEditorContainer.style.minHeight = '80px';
      }
    }

    replyEditors[commentId].focus();
  } else {
    form.style.display = 'none';
  }
}

function toggleMainCommentForm() {
  const form = document.getElementById('main-comment-form');
  if (commentEditor) {
    commentEditor.focus();
  }
}

// Handle form submissions with Quill content
function handleFormSubmission() {
  // Handle main comment form
  const mainForm = document.querySelector('#main-comment-form form');
  if (mainForm) {
    mainForm.addEventListener('submit', function(e) {
      const content = commentEditor.root.innerHTML;
      document.getElementById('comment-content').value = content;
    });
  }

  // Handle reply forms
  document.querySelectorAll('.reply-form form').forEach(form => {
    form.addEventListener('submit', function(e) {
      const formElement = e.target;
      const commentId = formElement.querySelector('input[name="parent_id"]').value;
      if (replyEditors[commentId]) {
        const content = replyEditors[commentId].root.innerHTML;
        formElement.querySelector('input[name="content"]').value = content;
      }
    });
  });
}

// Attach vote event listeners
document.addEventListener('DOMContentLoaded', function() {
  document.querySelectorAll('.vote-btn').forEach(button => {
    button.addEventListener('click', handleVote);
  });

  // Initialize editors
  initializeEditors();
  handleFormSubmission();
});
</script>

{% block style %}
/* Post detail specific styles */
.post-detail-page {
  max-width: 1200px;
  margin: 0 auto;
}

.breadcrumb-nav {
  margin-bottom: var(--spacing-lg);
}

.main-post {
  background: var(--color-bg-tertiary);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.main-post .post-title h1 {
  font-size: var(--font-size-2xl);
  margin: var(--spacing-md) 0;
  color: var(--color-text-white);
  line-height: var(--line-height-tight);
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

.main-comment-form {
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--color-border);
}

.comments-section {
  margin-top: var(--spacing-xl);
}

.comments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--color-border);
}

.comment-thread {
  margin-bottom: var(--spacing-lg);
}

.comment-item {
  display: flex;
  background: var(--color-bg-quaternary);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-sm);
  margin-bottom: var(--spacing-sm);
  transition: all var(--transition-fast);
}

.comment-item:hover {
  border-color: var(--color-primary-alpha);
}

.comment-vote-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-sm);
  background: var(--color-bg-secondary);
  border-right: 1px solid var(--color-border);
  min-width: 50px;
}

.comment-content-section {
  flex: 1;
  padding: var(--spacing-md);
  min-width: 0;
}

.comment-meta {
  font-size: var(--font-size-xs);
  color: var(--color-text-tertiary);
  margin-bottom: var(--spacing-xs);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  flex-wrap: wrap;
}

.comment-text {
  color: var(--color-text-primary);
  line-height: var(--line-height-relaxed);
  margin: var(--spacing-sm) 0;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

.reply-form {
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--color-border-light);
}

.comment-replies {
  border-left: 2px solid var(--color-primary-alpha);
  margin-left: var(--spacing-md);
  padding-left: var(--spacing-md);
  margin-top: var(--spacing-md);
}

/* Quill editor styling */
.comment-editor, .reply-editor {
  background: var(--color-bg-secondary);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-border);
}

.comment-editor .ql-toolbar,
.reply-editor .ql-toolbar {
  background: var(--color-bg-quaternary);
  border-bottom: 1px solid var(--color-border);
  border-radius: var(--radius-md) var(--radius-md) 0 0;
}

.comment-editor .ql-container,
.reply-editor .ql-container {
  background: var(--color-bg-secondary);
  color: var(--color-text-primary);
  border-radius: 0 0 var(--radius-md) var(--radius-md);
  font-family: inherit;
}

.comment-editor .ql-editor,
.reply-editor .ql-editor {
  color: var(--color-text-primary);
  line-height: var(--line-height-relaxed);
  font-size: var(--font-size-base);
  padding: var(--spacing-md);
}

.comment-editor .ql-editor.ql-blank::before,
.reply-editor .ql-editor.ql-blank::before {
  color: var(--color-text-tertiary);
  font-style: normal;
}

/* Quill toolbar button styling */
.ql-toolbar .ql-stroke {
  stroke: var(--color-text-secondary);
}

.ql-toolbar .ql-fill {
  fill: var(--color-text-secondary);
}

.ql-toolbar button:hover .ql-stroke,
.ql-toolbar button.ql-active .ql-stroke {
  stroke: var(--color-primary);
}

.ql-toolbar button:hover .ql-fill,
.ql-toolbar button.ql-active .ql-fill {
  fill: var(--color-primary);
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-bg-secondary);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: var(--radius-sm);
  transition: background var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary-alpha);
}

::-webkit-scrollbar-corner {
  background: var(--color-bg-secondary);
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--color-border) var(--color-bg-secondary);
}

/* Responsive design */
@media (max-width: 768px) {
  .main-post, .comment-item {
    flex-direction: column;
  }

  .post-vote-section, .comment-vote-section {
    flex-direction: row;
    justify-content: center;
    border-right: none;
    border-bottom: 1px solid var(--color-border);
    min-width: auto;
    padding: var(--spacing-sm);
  }

  .vote-buttons {
    flex-direction: row;
    gap: var(--spacing-md);
  }

  .comments-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .comment-content-section {
    padding: var(--spacing-sm);
  }

  .comment-meta {
    font-size: var(--font-size-xs);
    flex-wrap: wrap;
  }

  .main-post .post-title h1 {
    font-size: var(--font-size-xl);
    line-height: var(--line-height-normal);
  }

  .breadcrumb-nav {
    margin-bottom: var(--spacing-md);
  }

  .post-detail-page {
    padding: 0 var(--spacing-sm);
  }
}

@media (max-width: 576px) {
  .post-detail-page {
    padding: 0 var(--spacing-sm);
  }

  .comment-replies {
    margin-left: var(--spacing-sm);
    padding-left: var(--spacing-sm);
  }

  .comment-thread[style*="margin-left"] {
    margin-left: var(--spacing-sm) !important;
  }
}

{% endblock %}
{% endblock %}
