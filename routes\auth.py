from flask import Blueprint, render_template, request, redirect, flash, url_for
from models import Application, User, InviteCode
from werkzeug.security import generate_password_hash
from flask_mail import Message
from extensions import db, login_manager
from models import User
from werkzeug.security import check_password_hash
from flask_login import login_user, logout_user, login_required

import random, string
from flask_login import login_user

auth_bp = Blueprint("auth", __name__)

def generate_invite_code(length=12):
    return ''.join(random.choices(string.ascii_uppercase + string.digits, k=length))

def send_invite_email(email, code):
    msg = Message("Your Club Invite Code", recipients=[email], body=f"Your code: {code}")
    print(msg)

@auth_bp.route("/apply", methods=["GET","POST"])
def apply():
    if request.method == "POST":
        email = request.form["email"]
        username = request.form["username"]
        name = request.form.get("name")
        tier = request.form.get("tier")
        reason = request.form.get("reason")

        app_entry = Application(email=email, username=username, name=name, tier=tier, reason=reason)
        db.session.add(app_entry)
        db.session.commit()
        flash("Application submitted. Await admin approval.")
        return redirect("/apply")

    return render_template("apply.html")

@auth_bp.route("/register", methods=["GET","POST"])
def register():
    if request.method == "POST":
        email = request.form["email"]
        username = request.form["username"]
        password = request.form["password"]
        code_input = request.form["invite_code"]

        invite = InviteCode.query.filter_by(email=email, code=code_input, used=False).first()
        if not invite:
            flash("Invalid or expired invite code")
            return redirect("/register")

        user = User(email=email, username=username, password_hash=generate_password_hash(password))
        db.session.add(user)

        invite.used = True
        db.session.commit()
        flash("Account created")
        login_user(user)
        return redirect("/dashboard")

    return render_template("register.html")

@auth_bp.route("/login", methods=["GET", "POST"])
def login():
    if request.method == "POST":
        email = request.form["email"]
        password = request.form["password"]

        user = User.query.filter_by(email=email).first()
        if user and check_password_hash(user.password_hash, password):
            login_user(user)
            flash("Logged in successfully")
            return redirect(url_for("dash.dashboard"))
        else:
            flash("Invalid email or password")
            return redirect(url_for("auth.login"))

    return render_template("login.html")

@auth_bp.route("/logout")
@login_required
def logout():
    logout_user()
    flash("You have been logged out.")
    return redirect(url_for("auth.login"))