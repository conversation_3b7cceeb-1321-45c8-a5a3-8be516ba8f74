{% extends "layout.html" %}
{% block content %}
<div class="application-form">
  <div class="text-center mb-4">
    <h2>Apply to <PERSON><PERSON></h2>
    <p class="lead">Start your journey with our exclusive community</p>
    <p>Please fill out the form below to submit your application. We review each application carefully and will respond within 48 hours.</p>
  </div>

  <form method="POST" class="needs-validation" novalidate>
    <div class="row">
      <div class="col-md-6">
        <div class="form-group">
          <label for="email" class="form-label required">Email Address</label>
          <input type="email" class="form-control" id="email" name="email" placeholder="<EMAIL>" required>
          <div class="invalid-feedback">
            Please provide a valid email address.
          </div>
          <div class="form-text">We'll use this to contact you about your application.</div>
        </div>
      </div>

      <div class="col-md-6">
        <div class="form-group">
          <label for="username" class="form-label required">Username</label>
          <input type="text" class="form-control" id="username" name="username" placeholder="Choose a unique username" required>
          <div class="invalid-feedback">
            Please choose a username.
          </div>
          <div class="form-text">This will be your display name in the community.</div>
        </div>
      </div>
    </div>

    <div class="form-group">
      <label for="name" class="form-label">Full Name</label>
      <input type="text" class="form-control" id="name" name="name" placeholder="Your full name (optional)">
      <div class="form-text">Optional: Helps us personalize your experience.</div>
    </div>

    <div class="form-group">
      <label for="tier" class="form-label required">Desired Membership Tier</label>
      <select class="form-select" id="tier" name="tier" required>
        <option value="">Select a membership tier</option>
        <option value="Sentinel">Sentinel (Bronze) - $100/month - Full community access</option>
        <option value="Contributor">Contributor (Gold) - $250/month - Support community growth</option>
        <option value="Patron">Patron (Platinum) - $500/month - Maximum community support</option>
      </select>
      <div class="invalid-feedback">
        Please select a membership tier.
      </div>
      <div class="form-text">All tiers provide identical access to discussion boards, AMAs, premium events, and curated content. Gold and Platinum tiers are for members who want to support the community's growth and development. <a href="/#tiers">View detailed comparison</a>.</div>
    </div>

    <div class="form-group">
      <label for="reason" class="form-label required">Why do you want to join Vallum?</label>
      <textarea class="form-control" id="reason" name="reason" rows="4" placeholder="Tell us about your goals, interests, and what you hope to gain from joining our community..." required></textarea>
      <div class="invalid-feedback">
        Please tell us why you'd like to join.
      </div>
      <div class="form-text">Help us understand your motivations and how you'd contribute to our community.</div>
    </div>

    <div class="form-group">
      <div class="form-check">
        <input class="form-check-input" type="checkbox" id="terms" required>
        <label class="form-check-label" for="terms">
          I agree to uphold the values of respect, integrity, and confidentiality as outlined in our <a href="/#values">community values</a>.
        </label>
        <div class="invalid-feedback">
          You must agree to our community values to proceed.
        </div>
      </div>
    </div>

    <div class="text-center">
      <button type="submit" class="btn btn-primary btn-lg">
        <span class="btn-text">Submit Application</span>
      </button>
      <div class="mt-3">
        <small class="text-muted">
          Already have an account? <a href="/login">Sign in here</a>
        </small>
      </div>
    </div>
  </form>
</div>

<script>
// Enhanced form validation
(function() {
  'use strict';

  const form = document.querySelector('.needs-validation');

  form.addEventListener('submit', function(event) {
    if (!form.checkValidity()) {
      event.preventDefault();
      event.stopPropagation();
    }

    form.classList.add('was-validated');
  }, false);

  // Real-time validation feedback
  const inputs = form.querySelectorAll('input, select, textarea');
  inputs.forEach(input => {
    input.addEventListener('blur', function() {
      if (this.checkValidity()) {
        this.classList.remove('is-invalid');
        this.classList.add('is-valid');
      } else {
        this.classList.remove('is-valid');
        this.classList.add('is-invalid');
      }
    });
  });
})();
</script>
{% endblock %}
