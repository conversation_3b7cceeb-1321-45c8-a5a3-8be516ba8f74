{% extends "layout.html" %}
{% block content %}
<div class="admin-settings">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <div>
      <h2 class="mb-1">Community Settings</h2>
      <p class="text-muted mb-0">Manage boards</p>
    </div>
    <div class="admin-nav d-flex gap-2">
      <a href="/admin/applications" class="btn btn-outline-secondary">Applications</a>
      <a href="/admin/board-requests" class="btn btn-outline-secondary">Board Requests</a>
      <a href="/admin/members" class="btn btn-outline-secondary">Members</a>
      <a href="/admin/settings/boards" class="btn btn-primary">Boards</a>
    </div>
  </div>

  <div class="card bg-dark border-secondary mb-4">
    <div class="card-body">
      <h5 class="card-title">Add Board</h5>
      <form action="/admin/settings/boards/add" method="post" class="row gy-2 gx-2 align-items-end">
        <div class="col-md-4">
          <label class="form-label">Name</label>
          <input type="text" name="name" class="form-control" placeholder="e.g. General" required />
        </div>
        <div class="col-md-6">
          <label class="form-label">Description (optional)</label>
          <input type="text" name="description" class="form-control" placeholder="What is this board about?" />
        </div>
        <div class="col-md-2">
          <button type="submit" class="btn btn-primary w-100">Add</button>
        </div>
      </form>
    </div>
  </div>

  <div class="table-responsive">
    <table class="table table-striped align-middle">
      <thead>
        <tr>
          <th scope="col">Name</th>
          <th scope="col">Description</th>
          <th scope="col">Status</th>
          <th scope="col">Actions</th>
        </tr>
      </thead>
      <tbody>
        {% for b in boards %}
        <tr>
          <td class="col-name"><strong class="text-white">{{ b.name }}</strong></td>
          <td class="col-desc"><span class="text-muted">{{ b.description or '—' }}</span></td>
          <td class="col-status">
            {% if b.is_active %}
              <span class="badge bg-success">Active</span>
            {% else %}
              <span class="badge bg-secondary">Inactive</span>
            {% endif %}
          </td>
          <td class="col-actions">
            <div class="d-flex gap-2 flex-wrap">
              <a href="/admin/settings/boards/{{ b.id }}/toggle" class="btn btn-sm btn-outline-warning">
                {{ 'Deactivate' if b.is_active else 'Activate' }}
              </a>
              <a href="/admin/settings/boards/{{ b.id }}/delete" class="btn btn-sm btn-outline-danger" onclick="return confirm('Delete board {{ b.name }}? Existing posts will keep their board label, but this board will no longer be selectable.');">Delete</a>
            </div>
          </td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
</div>

{% endblock %}

