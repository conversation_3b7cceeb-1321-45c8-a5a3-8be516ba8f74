{% extends "layout.html" %}
{% block content %}
<div class="create-post-page">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="mb-0">Create New Post</h2>
    <a href="{{ url_for('dash.dashboard') }}" class="btn btn-outline-secondary">
      <i class="bi bi-arrow-left me-2"></i>Back to Board
    </a>
  </div>

  <form method="POST" action="{{ url_for('dash.create_post') }}" id="createPostFormPage" class="needs-validation" novalidate>
    <input type="hidden" name="content" id="hiddenContentPage">

    <div class="mb-3">
      <label for="boardSelect" class="form-label required">Board</label>
      <select id="boardSelect" name="board" class="form-select" required>
        {% set sel = selected_board or 'General' %}
        {% if boards %}
          {% for b in boards %}
            <option value="{{ b.name }}" {{ 'selected' if b.name == sel else '' }}>{{ b.name }}</option>
          {% endfor %}
        {% else %}
          <option value="General" selected>General</option>
        {% endif %}
      </select>
      <div class="form-text">Choose where to post. Only active boards are listed.</div>
    </div>

    <div class="row g-4">
      <div class="col-lg-6">
        <div class="mb-3">
          <label for="postTitlePage" class="form-label required">Post Title</label>
          <input type="text" class="form-control" id="postTitlePage" name="title" placeholder="What would you like to discuss?" required maxlength="200">
          <div class="invalid-feedback">Please provide a title for your post.</div>
        </div>

        <div class="mb-3">
          <label class="form-label required">Post Content</label>
          <div id="postContentEditorPage" class="rich-text-editor"></div>
          <div class="form-text">Use the toolbar to format your content. Be respectful and constructive.</div>
          <div class="invalid-feedback" id="contentFeedbackPage" style="display:none;">Please write at least 10 characters for your post content.</div>
        </div>

        <div class="d-flex gap-2">
          <button type="submit" class="btn btn-primary" id="submitPostBtnPage">
            <i class="bi bi-send me-2"></i>Publish Post
          </button>
          <button type="button" class="btn btn-secondary" id="resetFormBtn">Reset</button>
        </div>
      </div>

      <div class="col-lg-6">
        <div class="preview-card">
          <div class="preview-header d-flex align-items-center justify-content-between">
            <h5 class="mb-0">Live Preview</h5>
            <span class="text-muted small">As it will appear</span>
          </div>
          <div class="preview-body">
            <div class="reddit-post">
              <div class="post-vote-section">
                <div class="vote-buttons">
                  <button class="vote-btn" type="button" disabled><i class="bi bi-arrow-up"></i></button>
                  <span class="vote-score">0</span>
                  <button class="vote-btn" type="button" disabled><i class="bi bi-arrow-down"></i></button>
                </div>
              </div>
              <div class="post-content-section">
                <div class="post-header">
                  <div class="post-meta">
                    <span class="post-board">r/<span id="previewBoard">{{ selected_board or 'General' }}</span></span>
                    <span class="post-separator">•</span>
                    <span class="post-author">Posted by <span class="username">you</span></span>
                    <span class="post-separator">•</span>
                    <span class="post-time">just now</span>
                  </div>
                </div>
                <div class="post-title">
                  <h4 id="previewTitle">Your title will appear here</h4>
                </div>
                <div class="post-body">
                  <div class="post-text" id="previewContent">Start typing to see a live preview of your content...</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </form>
</div>

<script>
let pageQuill = null;

function initCreatePostPage() {
  try {
    pageQuill = new Quill('#postContentEditorPage', {
      theme: 'snow',
      placeholder: 'Share your thoughts, questions, or insights...',
      modules: { toolbar: [
        [{ 'header': [1, 2, 3, false] }],
        ['bold', 'italic', 'underline', 'strike'],
        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
        ['blockquote', 'code-block'],
        ['link'],
        ['clean']
      ]}
    });

    // Set minimum height for the editor
    const editorContainer = document.querySelector('#postContentEditorPage .ql-container');
    if (editorContainer) editorContainer.style.minHeight = '200px';

    // Live preview updates
    const previewTitle = document.getElementById('previewTitle');
    const previewContent = document.getElementById('previewContent');
    const titleInput = document.getElementById('postTitlePage');
    const boardSelect = document.getElementById('boardSelect');
    const previewBoard = document.getElementById('previewBoard');

    titleInput.addEventListener('input', () => {
      previewTitle.textContent = titleInput.value.trim() || 'Your title will appear here';
    });

    if (boardSelect && previewBoard) {
      previewBoard.textContent = boardSelect.value;
      boardSelect.addEventListener('change', () => {
        previewBoard.textContent = boardSelect.value;
      });
    }

    pageQuill.on('text-change', () => {
      const html = pageQuill.root.innerHTML;
      const text = pageQuill.getText().trim();
      previewContent.innerHTML = text ? html : 'Start typing to see a live preview of your content...';
    });

    // Form submission with validation
    const form = document.getElementById('createPostFormPage');
    form.addEventListener('submit', function(event) {
      event.preventDefault();
      event.stopPropagation();

      let isValid = true;
      const titleVal = titleInput.value.trim();
      const quillText = pageQuill.getText().trim();
      const quillHtml = pageQuill.root.innerHTML;

      // Title validation
      if (!titleVal) {
        titleInput.classList.add('is-invalid');
        isValid = false;
      } else {
        titleInput.classList.remove('is-invalid');
        titleInput.classList.add('is-valid');
      }

      // Content validation
      const contentFeedback = document.getElementById('contentFeedbackPage');
      if (quillText.length < 10) {
        if (contentFeedback) contentFeedback.style.display = 'block';
        isValid = false;
      } else {
        if (contentFeedback) contentFeedback.style.display = 'none';
      }

      if (isValid) {
        document.getElementById('hiddenContentPage').value = quillHtml;
        const btn = document.getElementById('submitPostBtnPage');
        const original = btn.innerHTML;
        btn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Publishing...';
        btn.disabled = true;
        form.submit();
      } else {
        form.classList.add('was-validated');
      }
    });

    // Reset handler
    document.getElementById('resetFormBtn').addEventListener('click', () => {
      document.getElementById('createPostFormPage').reset();
      titleInput.classList.remove('is-valid', 'is-invalid');
      try { pageQuill.setContents([]); } catch (e) {}
      previewTitle.textContent = 'Your title will appear here';
      previewContent.textContent = 'Start typing to see a live preview of your content...';
    });

  } catch (error) {
    console.error('Error initializing Create Post page:', error);
  }
}

document.addEventListener('DOMContentLoaded', initCreatePostPage);
</script>

<style>
.create-post-page .preview-card {
  background: var(--color-bg-tertiary);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
}
.preview-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--color-border);
}
.preview-body { padding: var(--spacing-md); }

/* Tighten up the embedded preview post */
.preview-body .reddit-post { border: 1px dashed var(--color-border); }
.preview-body .post-title h4 { margin: 0; }
</style>
{% endblock %}

